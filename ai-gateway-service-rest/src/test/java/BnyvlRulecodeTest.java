import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.Pii;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.rulecode.vendors.BNYVL;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.service.vendor.bnyvl.AddressStatusMapReader;
import me.socure.aigateway.service.vendor.bnyvl.BNYVLAdapter;
import me.socure.aigateway.service.vendor.bnyvl.CoverageRoutingNumberReader;
import me.socure.aigateway.service.vendor.bnyvl.model.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BnyvlRulecodeTest {

    @Test
    public void testBNYVLNonDerivedRuleCodes() throws Exception {
        String csvFilePath = "src/test/resources/bnyvl/RulecodeTestData.csv";
        String[][] csvData = CSVReaderAndParser.readCsv(csvFilePath);

        HashMap<String, String> ruleMap = new HashMap<>();
        ruleMap.put("BNYM_ASV_STATUS_CODE", "bnyvl100001");
        ruleMap.put("BNYM_ASV_DESCRIPTION", "bnyvl100002");
        ruleMap.put("BNYM_AOV_STATUS_CODE", "bnyvl100003");
        ruleMap.put("BNYM_FNAME", "bnyvl100004");
        ruleMap.put("BNYM_LNAME", "bnyvl100005");
        ruleMap.put("BNYM_NAME", "bnyvl100006");
        ruleMap.put("BNYM_BUSNAME", "bnyvl100007");
        ruleMap.put("BNYM_SSN", "bnyvl100008");
        ruleMap.put("BNYM_DOB", "bnyvl100009");
        ruleMap.put("BNYM_ADDRESS", "bnyvl100010");
        ruleMap.put("BNYM_CITY", "bnyvl100011");
        ruleMap.put("BNYM_STATE", "bnyvl100012");
        ruleMap.put("BNYM_ZIP", "bnyvl100013");
        ruleMap.put("BNYM_HMPHONE", "bnyvl100014");
        ruleMap.put("BNYM_WKPHONE", "bnyvl100021");
        ruleMap.put("BNYM_PHONE", "bnyvl100022");
        ruleMap.put("BNYM_MATCH_SCORE", "bnyvl100015");
        ruleMap.put("BNYM_FNAME_MATCH", "bnyvl100016");
        ruleMap.put("BNYM_LNAME_MATCH", "bnyvl100017");
        ruleMap.put("BNYM_EWS_DIRECT_CONTRIBUTOR", "bnyvl100020");
        ruleMap.put("BNYM_AOV_DESCRIPTION", "bnyvl100123");
        ruleMap.put("BNYM_AOV_NOT_AVALIABLE", "bnyvl100124");



        String[] headers = csvData[0];
        List<TestUtils.RowTest> rowTests = new ArrayList<>();
        CoverageRoutingNumberReader coverageRoutingNumberReader = new CoverageRoutingNumberReader();
        coverageRoutingNumberReader.run(null);

        // Create target instance of BNYVL.
        Class<?> vendorClass = BNYVL.class;
        Constructor<?> constructor = vendorClass.getDeclaredConstructor(CoverageRoutingNumberReader.class, AddressStatusMapReader.class);
        constructor.setAccessible(true);
        Object targetInstance = constructor.newInstance(coverageRoutingNumberReader, null);

        // Loop over each data row (starting at index 1).
        for (int i = 1; i < csvData.length; i++) {
            final int rowIndex = i;
            rowTests.add(new TestUtils.RowTest(rowIndex, () -> {
                String[] rowData = csvData[rowIndex];
                Map<String, String> rowMap = new HashMap<>();
                for (int j = 0; j < headers.length && j < rowData.length; j++) {
                    rowMap.put(headers[j].trim(), rowData[j].trim());
                }

                AIVendorRequest request = AIVendorRequest.builder()
                        .piis(Pii.builder()
                                .routingNumber(rowMap.getOrDefault("routingNumber", "")).build())
                        .build();

                Data data = Data.builder()
                        .status(Status.builder()
                                .aoa(AOA.builder()
                                        .statusCode(rowMap.getOrDefault("bny_aoaStatusCode", ""))
                                        .description(rowMap.getOrDefault("bny_aoaDescription", ""))
                                        .build())
                                .asv(ASV.builder()
                                        .statusCode(rowMap.getOrDefault("bny_asvStatusCode", ""))
                                        .description(rowMap.getOrDefault("bny_asvDescription", ""))
                                        .build())
                                .build())
                        .build();

                BnyMellonResponse resp = BnyMellonResponse.builder()
                        .result(Result.builder().data(new Data[]{data}).build())
                        .build();

                VendorParsedResponse vendorResponse = VendorParsedResponse.builder()
                        .vendorName("BNYVL")
                        .status(200)
                        .parsedData(resp)
                        .build();

                // Pre-process rule codes using BNYVLAdapter.
                BNYVLAdapter bnyvlAdapter = new BNYVLAdapter(null, null, null, null);
                Data preProcessedData = bnyvlAdapter.preProcessRuleCodes(vendorResponse, request);
                Assertions.assertNotNull(preProcessedData, "Row " + rowIndex + ": Pre-processed Data should not be null");

                List<org.junit.jupiter.api.function.Executable> ruleTests = new ArrayList<>();
                for (Map.Entry<String, String> ruleEntry : ruleMap.entrySet()) {
                    final String csvColumn = ruleEntry.getKey();
                    final String ruleMethodName = ruleEntry.getValue();
                    ruleTests.add(() -> {
                        Method ruleMethod = vendorClass.getMethod(ruleMethodName, AIVendorRequest.class, VendorParsedResponse.class, Data.class);
                        Object actualResult = ruleMethod.invoke(targetInstance, request, vendorResponse, preProcessedData);
                        String expectedValue = rowMap.get(csvColumn).isEmpty() ? null : rowMap.get(csvColumn);
                        TestUtils.assertRuleResult(rowIndex, ruleMethodName, expectedValue, actualResult);
                    });
                }
                Assertions.assertAll("Row " + rowIndex + " rule assertions", ruleTests);
            }));
        }
        TestUtils.executeRowTests(rowTests);
    }


    @Test
    public void testSuccessRuleCode() throws Exception {
        String csvFilePath = "src/test/resources/bnyvl/SuccessRuleTestData.csv";
        String[][] csvData = CSVReaderAndParser.readCsv(csvFilePath);

        HashMap<String, String> ruleMap = new HashMap<>();
        ruleMap.put("BNYM_RESPONSE_OK", "bnyvl100135");

        String[] headers = csvData[0];
        List<TestUtils.RowTest> rowTests = new ArrayList<>();

        // Create target instance of BNYVL.
        Class<?> vendorClass = BNYVL.class;
        Constructor<?> constructor = vendorClass.getDeclaredConstructor(CoverageRoutingNumberReader.class, AddressStatusMapReader.class);
        constructor.setAccessible(true);
        Object targetInstance = constructor.newInstance(null, null);

        // Loop over each data row (starting at index 1).
        for (int i = 1; i < csvData.length; i++) {
            final int rowIndex = i;
            rowTests.add(new TestUtils.RowTest(rowIndex, () -> {
                String[] rowData = csvData[rowIndex];
                Map<String, String> rowMap = new HashMap<>();
                for (int j = 0; j < headers.length && j < rowData.length; j++) {
                    rowMap.put(headers[j].trim(), rowData[j].trim());
                }

                AIVendorRequest request = AIVendorRequest.builder().piis(Pii.builder().build()).build();
                BnyMellonResponse resp = BnyMellonResponse.builder().metadata(
                        Metadata.builder().success(
                                "true".equalsIgnoreCase(rowMap.get("success"))
                        ).build()
                ).build();

                VendorParsedResponse vendorResponse = VendorParsedResponse.builder()
                        .vendorName("BNYVL")
                        .status(200)
                        .parsedData(resp)
                        .build();

                List<org.junit.jupiter.api.function.Executable> ruleTests = new ArrayList<>();
                for (Map.Entry<String, String> ruleEntry : ruleMap.entrySet()) {
                    final String csvColumn = ruleEntry.getKey();
                    final String ruleMethodName = ruleEntry.getValue();
                    ruleTests.add(() -> {
                        Method ruleMethod = vendorClass.getMethod(ruleMethodName, AIVendorRequest.class, VendorParsedResponse.class, Data.class);
                        Object actualResult = ruleMethod.invoke(targetInstance, request, vendorResponse, null);
                        String expectedValue = rowMap.get(csvColumn).isEmpty() ? null : rowMap.get(csvColumn);
                        TestUtils.assertRuleResult(rowIndex, ruleMethodName, expectedValue, actualResult);
                    });
                }
                Assertions.assertAll("Row " + rowIndex + " rule assertions", ruleTests);
            }));
        }
        TestUtils.executeRowTests(rowTests);
    }

    @Test
    public void testBNYVLDerivedRuleCodes() throws Exception {
        String csvFilePath = "src/test/resources/bnyvl/RulecodeTestData.csv";
        String[][] csvData = CSVReaderAndParser.readCsv(csvFilePath);

        HashMap<String, DerivedRuleCodeInfo> ruleMap = getDerivedRuleCodeInfoHashMap();

        String[] headers = csvData[0];
        List<TestUtils.RowTest> rowTests = new ArrayList<>();
        CoverageRoutingNumberReader coverageRoutingNumberReader = new CoverageRoutingNumberReader();
        coverageRoutingNumberReader.run(null);
        AddressStatusMapReader addressStatusMapReader = new AddressStatusMapReader();
        addressStatusMapReader.run(null);

        // Create target instance of BNYVL.
        Class<?> vendorClass = BNYVL.class;
        Constructor<?> constructor = vendorClass.getDeclaredConstructor(CoverageRoutingNumberReader.class, AddressStatusMapReader.class);
        constructor.setAccessible(true);
        Object targetInstance = constructor.newInstance(coverageRoutingNumberReader, addressStatusMapReader);

        // Loop over each data row (starting at index 1).
        for (int i = 1; i < csvData.length; i++) {
            final int rowIndex = i;
            rowTests.add(new TestUtils.RowTest(rowIndex, () -> {
                String[] rowData = csvData[rowIndex];
                Map<String, String> rowMap = new HashMap<>();
                for (int j = 0; j < headers.length && j < rowData.length; j++) {
                    rowMap.put(headers[j].trim(), rowData[j].trim());
                }

                AIVendorRequest request = AIVendorRequest.builder()
                        .piis(Pii.builder()
                                .routingNumber(rowMap.getOrDefault("routing_number", "")).build())
                        .build();

                Data data = Data.builder()
                        .status(Status.builder()
                                .aoa(AOA.builder()
                                        .statusCode(rowMap.getOrDefault("bny_aoaStatusCode", ""))
                                        .description(rowMap.getOrDefault("bny_aoaDescription", ""))
                                        .build())
                                .asv(ASV.builder()
                                        .statusCode(rowMap.getOrDefault("bny_asvStatusCode", ""))
                                        .description(rowMap.getOrDefault("bny_asvDescription", ""))
                                        .build())
                                .build())
                        .build();

                BnyMellonResponse resp = BnyMellonResponse.builder()
                        .result(Result.builder().data(new Data[]{data}).build())
                        .build();

                VendorParsedResponse vendorResponse = VendorParsedResponse.builder()
                        .vendorName("BNYVL")
                        .status(200)
                        .parsedData(resp)
                        .build();

                // Pre-process rule codes using BNYVLAdapter.
                BNYVLAdapter bnyvlAdapter = new BNYVLAdapter(null, null, null, null);
                Data preProcessedData = bnyvlAdapter.preProcessRuleCodes(vendorResponse, request);
                Assertions.assertNotNull(preProcessedData, "Row " + rowIndex + ": Pre-processed Data should not be null");

                List<org.junit.jupiter.api.function.Executable> ruleTests = new ArrayList<>();
                for (Map.Entry<String, DerivedRuleCodeInfo> ruleEntry : ruleMap.entrySet()) {
                    final String csvColumn = ruleEntry.getKey();
                    final String ruleMethodName = ruleEntry.getValue().getMethodName();
                    DerivedRuleCodeInfo derivedRuleCodeInfo = ruleEntry.getValue();
                    ruleTests.add(() -> {
                        Method ruleMethod = vendorClass.getMethod(ruleMethodName, AIVendorRequest.class, VendorParsedResponse.class, Data.class, AIVendorRuleCodes.class);
                        Object actualResult = ruleMethod.invoke(targetInstance, request, vendorResponse, preProcessedData, TestUtils.buildDerivedRuleCodeReq(derivedRuleCodeInfo, rowMap));
                        String expectedValue = rowMap.get(csvColumn).isEmpty() ? null : rowMap.get(csvColumn);
                        TestUtils.assertRuleResult(rowIndex, ruleMethodName, expectedValue, actualResult);
                    });
                }
                Assertions.assertAll("Row " + rowIndex + " rule assertions", ruleTests);
            }));
        }
        TestUtils.executeRowTests(rowTests);
    }

    @Test
    public void testInquireDateRule() throws Exception {
        String csvFilePath = "src/test/resources/bnyvl/EnquireDateRuleTestData.csv";
        String[][] csvData = CSVReaderAndParser.readCsv(csvFilePath);

        HashMap<String, DerivedRuleCodeInfo> ruleMap = getInquireDerivedRuleCode();

        String[] headers = csvData[0];
        List<TestUtils.RowTest> rowTests = new ArrayList<>();
        CoverageRoutingNumberReader coverageRoutingNumberReader = new CoverageRoutingNumberReader();
        coverageRoutingNumberReader.run(null);
        AddressStatusMapReader addressStatusMapReader = new AddressStatusMapReader();
        addressStatusMapReader.run(null);

        // Create target instance of BNYVL.
        Class<?> vendorClass = BNYVL.class;
        Constructor<?> constructor = vendorClass.getDeclaredConstructor(CoverageRoutingNumberReader.class, AddressStatusMapReader.class);
        constructor.setAccessible(true);
        Object targetInstance = constructor.newInstance(coverageRoutingNumberReader, addressStatusMapReader);

        // Loop over each data row (starting at index 1).
        for (int i = 1; i < csvData.length; i++) {
            final int rowIndex = i;
            rowTests.add(new TestUtils.RowTest(rowIndex, () -> {
                String[] rowData = csvData[rowIndex];
                Map<String, String> rowMap = new HashMap<>();
                for (int j = 0; j < headers.length && j < rowData.length; j++) {
                    rowMap.put(headers[j].trim(), rowData[j].trim());
                }

                AIVendorRequest request = AIVendorRequest.builder().build();



                BnyMellonResponse resp = BnyMellonResponse.builder()
                        .result(Result.builder().requestTimestamp(rowMap.getOrDefault("metadata_date", "")).build())
                        .build();

                VendorParsedResponse vendorResponse = VendorParsedResponse.builder()
                        .vendorName("BNYVL")
                        .status(200)
                        .parsedData(resp)
                        .build();

                List<org.junit.jupiter.api.function.Executable> ruleTests = new ArrayList<>();
                for (Map.Entry<String, DerivedRuleCodeInfo> ruleEntry : ruleMap.entrySet()) {
                    final String csvColumn = ruleEntry.getKey();
                    final String ruleMethodName = ruleEntry.getValue().getMethodName();
                    DerivedRuleCodeInfo derivedRuleCodeInfo = ruleEntry.getValue();
                    ruleTests.add(() -> {
                        Method ruleMethod = vendorClass.getMethod(ruleMethodName, AIVendorRequest.class, VendorParsedResponse.class, Data.class, AIVendorRuleCodes.class);
                        Object actualResult = ruleMethod.invoke(targetInstance, request, vendorResponse, null, TestUtils.buildDerivedRuleCodeReq(derivedRuleCodeInfo, rowMap));
                        String expectedValue = rowMap.get(csvColumn).isEmpty() ? null : rowMap.get(csvColumn);
                        TestUtils.assertRuleResult(rowIndex, ruleMethodName, expectedValue, actualResult);
                    });
                }
                Assertions.assertAll("Row " + rowIndex + " rule assertions", ruleTests);
            }));
        }
        TestUtils.executeRowTests(rowTests);
    }

    private static HashMap<String, DerivedRuleCodeInfo> getDerivedRuleCodeInfoHashMap() {
        HashMap<String, DerivedRuleCodeInfo> ruleMap = new HashMap<>();

        DerivedRuleCodeInfo derivedBNYMatchInfo = new DerivedRuleCodeInfo(
                "bnyvl100018derived", new HashMap<>(Map.of(
                "BNYM_ADDRESS", new String[]{"BNYVL.100010","C"},
                "BNYM_CITY", new String[]{"BNYVL.100011", "C"},
                "BNYM_STATE", new String[]{"BNYVL.100012", "C"},
                "BNYM_ZIP", new String[]{"BNYVL.100013","C"}
        ))
        );

        ruleMap.put("BNYM_ADDRESS_MATCH", derivedBNYMatchInfo);
        return ruleMap;
    }

    private static HashMap<String, DerivedRuleCodeInfo> getInquireDerivedRuleCode() {
        HashMap<String, DerivedRuleCodeInfo> ruleMap = new HashMap<>();

        DerivedRuleCodeInfo derivedEnquireDateMatchInfo = new DerivedRuleCodeInfo(
                "bnyvl100125derived", new HashMap<>(Map.of(
                "BNYM_AOV_STATUS_CODE", new String[]{"BNYVL.100003", "C"}
        ))
        );

        ruleMap.put("BNYM_INQUIRY_DATE", derivedEnquireDateMatchInfo);
        return ruleMap;
    }


}
