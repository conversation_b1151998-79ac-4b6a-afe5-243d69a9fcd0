import me.socure.aigateway.rulecode.util.Date;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

public class DateParsingTest {

    @Test
    public void testMMddyyyyFormat() {
        // Test the specific case mentioned: "07/26/2006"
        LocalDate result = Date.parseToLocalDate("07/26/2006");
        assertNotNull(result, "Date parsing should not return null for MM/dd/yyyy format");
        assertEquals(2006, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(26, result.getDayOfMonth());
    }

    @Test
    public void testVariousUSDateFormats() {
        // Test MM/dd/yyyy
        LocalDate date1 = Date.parseToLocalDate("12/31/2023");
        assertNotNull(date1);
        assertEquals(LocalDate.of(2023, 12, 31), date1);

        // Test M/d/yyyy (single digit month/day)
        LocalDate date2 = Date.parseToLocalDate("1/5/2023");
        assertNotNull(date2);
        assertEquals(LocalDate.of(2023, 1, 5), date2);

        // Test MM/dd/yy (2-digit year)
        LocalDate date3 = Date.parseToLocalDate("07/26/06");
        assertNotNull(date3);
        assertEquals(LocalDate.of(2006, 7, 26), date3);

        // Test M/d/yy
        LocalDate date4 = Date.parseToLocalDate("1/5/23");
        assertNotNull(date4);
        assertEquals(LocalDate.of(2023, 1, 5), date4);
    }

    @Test
    public void testExistingFormats() {
        // Ensure existing formats still work
        LocalDate date1 = Date.parseToLocalDate("2023-12-31");
        assertNotNull(date1);
        assertEquals(LocalDate.of(2023, 12, 31), date1);

        LocalDate date2 = Date.parseToLocalDate("2023/12/31");
        assertNotNull(date2);
        assertEquals(LocalDate.of(2023, 12, 31), date2);
    }

    @Test
    public void testInvalidDates() {
        // Test invalid formats still return null
        LocalDate result1 = Date.parseToLocalDate("invalid-date");
        assertNull(result1);

        LocalDate result2 = Date.parseToLocalDate("");
        assertNull(result2);

        LocalDate result3 = Date.parseToLocalDate(null);
        assertNull(result3);
    }
}
