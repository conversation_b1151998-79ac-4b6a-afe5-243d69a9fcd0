package me.socure.aigateway.rulecode.vendors.global.scorer;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class OwnershipBusinessScorer {
    private static final Map<AccountOwnershipBusinessScore, Double> ownershipBusinessScores;

    private OwnershipBusinessScorer() {
        // Prevent instantiation
    }

    static {
        try {
            List<AccountOwnershipBusinessScore> rows = new ArrayList<>();
            
            try (InputStreamReader inputStreamReader = new InputStreamReader(
                    Objects.requireNonNull(OwnershipBusinessScorer.class.getResourceAsStream("/vendor/AccountOwnershipBusinessScores.csv"),
                            "Resource file '/vendor/AccountOwnershipBusinessScores.csv' not found"))) {
                try (BufferedReader reader = new BufferedReader(inputStreamReader)) {
                    String header = reader.readLine();
                    if (header == null) {
                        throw new RuntimeException("CSV file is empty");
                    }
                    
                    String line;
                    while ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",");
                        if (parts.length >= 5) {
                            try {
                                AccountOwnershipBusinessScore score = new AccountOwnershipBusinessScore(
                                    parts[0].trim(),
                                    parts[1].trim(),
                                    parts[2].trim(),
                                    parts[3].trim(),
                                    Double.parseDouble(parts[4].trim())
                                );
                                rows.add(score);
                            } catch (NumberFormatException e) {
                                // Skip malformed lines
                                System.err.println("Skipping malformed line in AccountOwnershipBusinessScores.csv: " + line);
                            }
                        }
                    }
                }
            }
            
            ownershipBusinessScores = rows.stream()
                .collect(Collectors.toMap(
                    score -> score,
                    AccountOwnershipBusinessScore::aiAoAScore,
                    (existing, replacement) -> existing // Keep first value if duplicate keys
                ));
        } catch (Exception e) {
            throw new RuntimeException("Unable to process config for ownership business scores: " + e.getMessage(), e);
        }
    }

    public static Map<AccountOwnershipBusinessScore, Double> getScores() {
        return ownershipBusinessScores;
    }
}