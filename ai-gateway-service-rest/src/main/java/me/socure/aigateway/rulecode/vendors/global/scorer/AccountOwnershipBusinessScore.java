package me.socure.aigateway.rulecode.vendors.global.scorer;

public record AccountOwnershipBusinessScore(
    String aiBusName,
    String aiEIN,
    String aiPhone,
    String aiAddress,
    double aiAoAScore
) {
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof AccountOwnershipBusinessScore other)) return false;
        return safeEqualsIgnoreCase(aiBusName, other.aiBusName) &&
               safeEqualsIgnoreCase(aiEIN, other.aiEIN) &&
               safeEqualsIgnoreCase(aiPhone, other.aiPhone) &&
               safeEqualsIgnoreCase(aiAddress, other.aiAddress);
    }

    private boolean safeEqualsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equalsIgnoreCase(str2);
    }

    @Override
    public int hashCode() {
        String busName = aiBusName != null ? aiBusName.toLowerCase() : "null";
        String ein = aiEIN != null ? aiEIN.toLowerCase() : "null";
        String phone = aiPhone != null ? aiPhone.toLowerCase() : "null";
        String address = aiAddress != null ? aiAddress.toLowerCase() : "null";
        return (busName + "_" + ein + "_" + phone + "_" + address).hashCode();
    }

    public String getAuditString() {
        return String.format(" BusName %s, EIN: %s, Phone: %s, Address: %s, Score: %f",
                aiBusName, aiEIN, aiPhone, aiAddress, aiAoAScore);
    }
}
