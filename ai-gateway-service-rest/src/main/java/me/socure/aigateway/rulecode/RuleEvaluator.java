package me.socure.aigateway.rulecode;

import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.rulecode.vendors.MBTVL;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorAdapterFactory;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.service.vendor.mbtvl.model.NameValue;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class RuleEvaluator {

    private final VendorAdapterFactory vendorAdapterFactory;
    private final MBTVL mbtvl;

    public RuleEvaluator(VendorAdapterFactory vendorAdapterFactory, MBTVL mbtvl) {
        this.vendorAdapterFactory = vendorAdapterFactory;
        this.mbtvl = mbtvl;
    }

    public Optional<String> evaluateMBTVL100004(AIGatewayRequest request, VendorRawResponse rawResponse) {
        VendorAdapter adapter = vendorAdapterFactory.getVendorAdapter("mbtvl");
        VendorParsedResponse parsed = adapter.processResponse(rawResponse);
        NameValue[] properties = adapter.preProcessRuleCodes(parsed, null);
        String result = mbtvl.mbtvl100004(null, parsed, properties);
        return Optional.ofNullable(result);
    }
}

