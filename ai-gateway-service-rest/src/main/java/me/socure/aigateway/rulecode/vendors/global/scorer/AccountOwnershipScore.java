package me.socure.aigateway.rulecode.vendors.global.scorer;

public record AccountOwnershipScore(
    String aiFirstName,
    String aiLastName,
    String aiSSN,
    String aiDOB,
    String aiPhone,
    String aiAddress,
    double aiAoAScore,
    double aiAoAScoreBand
) {
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof AccountOwnershipScore other)) return false;
        return safeEqualsIgnoreCase(aiFirstName, other.aiFirstName) &&
               safeEqualsIgnoreCase(aiLastName, other.aiLastName) &&
               safeEqualsIgnoreCase(aiSSN, other.aiSSN) &&
               safeEqualsIgnoreCase(aiDOB, other.aiDOB) &&
               safeEqualsIgnoreCase(aiPhone, other.aiPhone) &&
               safeEqualsIgnoreCase(aiAddress, other.aiAddress);
    }

    private boolean safeEqualsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equalsIgnoreCase(str2);
    }

    @Override
    public int hashCode() {
        String firstName = aiFirstName != null ? aiFirstName.toLowerCase() : "null";
        String lastName = aiLastName != null ? aiLastName.toLowerCase() : "null";
        String ssn = aiSSN != null ? aiSSN.toLowerCase() : "null";
        String dob = aiDOB != null ? aiDOB.toLowerCase() : "null";
        String phone = aiPhone != null ? aiPhone.toLowerCase() : "null";
        String address = aiAddress != null ? aiAddress.toLowerCase() : "null";
        return (firstName + "_" + lastName + "_" + ssn + "_" + dob + "_" + phone + "_" + address + "_").hashCode();
    }

    public String getAuditString() {
        return String.format(" FirstName %s, LastName: %s, SSN: %s, DOB: %s , Phone: %s, Address: %s, Score Band: %f, Score: %f",
                aiFirstName, aiLastName, aiSSN, aiDOB, aiPhone, aiAddress, aiAoAScoreBand, aiAoAScore);
    }
}
