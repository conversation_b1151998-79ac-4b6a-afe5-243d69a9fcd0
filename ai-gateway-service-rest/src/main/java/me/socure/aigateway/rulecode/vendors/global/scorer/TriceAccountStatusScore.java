package me.socure.aigateway.rulecode.vendors.global.scorer;

public record TriceAccountStatusScore(String triceAsvDecision, double score) {
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof TriceAccountStatusScore other)) return false;
        return safeEqualsIgnoreCase(triceAsvDecision, other.triceAsvDecision);
    }

    @Override
    public int hashCode() {
        String decision = triceAsvDecision != null ? triceAsvDecision.toLowerCase() : "null";
        return (decision + "_").hashCode();
    }

    private boolean safeEqualsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equalsIgnoreCase(str2);
    }

    public String getAuditString() {
        return String.format("Trice ASV Decision %s, Score: %f", triceAsvDecision, score);
    }
}
