package me.socure.aigateway.rulecode.util;

import lombok.experimental.UtilityClass;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

@UtilityClass
public class Date {
    public static Integer calculateDaysBetween(long submissionDateMillis, String otherDate, String dateFormat) {
        if (submissionDateMillis < 10_000_000_000L) {
            submissionDateMillis *= 1000;
        }

        LocalDate submissionDate = Instant.ofEpochMilli(submissionDateMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        try {
            LocalDate otherLocalDate = LocalDate.parse(otherDate, DateTimeFormatter.ofPattern(dateFormat));
            return Math.abs((int) (submissionDate.toEpochDay() - otherLocalDate.toEpochDay()));
        } catch (Exception e){
            return null;
        }
    }

    private static final List<DateTimeFormatter> SUPPORTED_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("yyyy"),
            DateTimeFormatter.ofPattern("yyyyMM"),
            DateTimeFormatter.ofPattern("MMM yyyy", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("yyyy-MM"),
            DateTimeFormatter.ofPattern("yyyyMMdd"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),  // Added support for MM/dd/yyyy format
            DateTimeFormatter.ofPattern("M/d/yyyy"),    // Added support for M/d/yyyy format (single digit month/day)
            DateTimeFormatter.ofPattern("MM/dd/yy"),    // Added support for MM/dd/yy format (2-digit year)
            DateTimeFormatter.ofPattern("M/d/yy"),      // Added support for M/d/yy format
            DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd H:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
    );

    public static LocalDate parseToLocalDate(String rawDateStr) {
        if (rawDateStr == null || rawDateStr.trim().isEmpty()) return null;

        String input = rawDateStr.trim();

        for (DateTimeFormatter formatter : SUPPORTED_FORMATTERS) {
            try {
                String pattern = formatter.toString();

                if (pattern.contains("yyyy") && !pattern.contains("d")) {
                    return YearMonth.parse(input, formatter).atDay(1);
                }

                if (pattern.equals("yyyy")) {
                    return Year.parse(input, formatter).atDay(1);
                }

                if (pattern.contains("HH") || pattern.contains("hh") || pattern.contains("H:")) {
                    return LocalDateTime.parse(input, formatter).toLocalDate();
                }

                return LocalDate.parse(input, formatter);

            } catch (DateTimeParseException ignored) {

            }
        }
        try {
            return LocalDate.parse(input);
        } catch (Exception ignored) {}

        return null;
    }
}
