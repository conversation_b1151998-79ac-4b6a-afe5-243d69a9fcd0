package me.socure.aigateway.rulecode.vendors.global;

import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.AIGatewayInquiries;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.response.AIGatewayScore;
import me.socure.aigateway.model.response.AIGatewayScoreBreakdown;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;
import me.socure.aigateway.rulecode.vendors.global.model.AvailabilityResult;
import me.socure.aigateway.rulecode.vendors.global.model.MbOnlyAvailabilityResult;
import me.socure.aigateway.rulecode.vendors.global.model.OwnershipResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static me.socure.aigateway.model.AIGatewayInquiries.AVAILABILITY;
import static me.socure.aigateway.model.AIGatewayInquiries.OWNERSHIP;

@Component
@Log4j2
public class Level0 {

    private final ScoreHelper scoreHelper;
    private final Scorer scorer;

    @Autowired
    public Level0(ScoreHelper scoreHelper, Scorer scorer) {
        this.scoreHelper = scoreHelper;
        this.scorer = scorer;
    }

    public AIVendorRuleCodes computeGlobals(AIGatewayRequest request, List<AiResponseData> vendorResponses) {
        Map<String, String> cRuleCodes = new HashMap<>();
        Map<String, Double> nRuleCodes = new HashMap<>();

        // Process all vendor responses
        for (AiResponseData res : vendorResponses) {
            if (res != null && res.getRulecodes() != null) {
                AIVendorRuleCodes ruleCodes = res.getRulecodes();

                if (ruleCodes.getCategoricalRulecodes() != null && !ruleCodes.getCategoricalRulecodes().isEmpty()) {
                    cRuleCodes.putAll(ruleCodes.getCategoricalRulecodes());
                }

                if (ruleCodes.getNumericalRulecodes() != null && !ruleCodes.getNumericalRulecodes().isEmpty()) {
                    nRuleCodes.putAll(ruleCodes.getNumericalRulecodes());
                }
            }
        }

        // Check if BNYVL was requested AND succeeded (has valid rulecodes)
        boolean bnyvlRequested = request.vendors().stream()
                .anyMatch(vendor -> vendor.getName().equalsIgnoreCase("BNYVL"));


        if (bnyvlRequested) {
            AvailabilityResult availabilityResult = scoreHelper.processAvailabilityRuleCodes(request, cRuleCodes);
            OwnershipResult ownershipResult = updateInquiries(request.payment().getInquiries()).contains(OWNERSHIP)
                    ? scoreHelper.processRuleCodes(request, cRuleCodes, nRuleCodes)
                    : null;
            AIGatewayScore scores = getScores(request, availabilityResult, ownershipResult);
            return scorer.updateRuleCodesAfterScoreComputation(scores, availabilityResult.getAsvPattern(),
                    availabilityResult.isAccountStructureIssueExists(), ownershipResult,
                    availabilityResult.getMaxLastSeenDate());
        } else {
            MbOnlyAvailabilityResult availabilityResult =
                    scoreHelper.processMbOnlyAvailabilityRuleCodes(request, cRuleCodes);

            OwnershipResult ownershipResult = null;
            Set<String> inquiries = updateInquiries(request.payment().getInquiries());
            if (inquiries.contains(AIGatewayInquiries.OWNERSHIP)) {
                ownershipResult = scoreHelper.processRuleCodes(request, cRuleCodes, nRuleCodes);
            }

            AIGatewayScore scores = getMbOnlyScores(request, availabilityResult, ownershipResult);

            return scorer.updateRuleCodesAfterScoreComputation(
                    scores,
                    availabilityResult.getAsvPattern(),
                    availabilityResult.isSaiAccountStructureIssue(),
                    ownershipResult,
                    availabilityResult.getMaxLastSeenDate()
            );
        }
    }

    private Set<String> updateInquiries(Set<String> inquiries) {
        boolean hasStatus = inquiries.stream()
                .anyMatch(i -> i.equalsIgnoreCase(AIGatewayInquiries.STATUS));

        if (!hasStatus) {
            return inquiries;
        }

        Set<String> updated = inquiries.stream()
                .filter(i -> !i.equalsIgnoreCase(AIGatewayInquiries.STATUS))
                .collect(Collectors.toCollection(HashSet::new));
        updated.add(AIGatewayInquiries.AVAILABILITY);

        return updated;
    }

    private AIGatewayScore getScores(AIGatewayRequest request, AvailabilityResult availabilityResult,
                                     OwnershipResult matchResult) {

        Set<String> inquirySet = updateInquiries(request.payment().getInquiries());

        // Case: only availability
        if (inquirySet.equals(Set.of(AVAILABILITY))) {
            double mbStatusScore = scorer.getMbOnlyAvailabilityScore(
                    availabilityResult.getMbDecision(),
                    availabilityResult.getMbLastSeenMulti(),
                    availabilityResult.getMbReturns()
            );
            double triceStatusScore = scorer.getTriceAvailabilityScore(
                    availabilityResult.getTriceAsvDecision()
            );
            double bnymStatusScore = scorer.getBnymAvailabilityScore(
                    availabilityResult.getBnymStatusCode(),
                    availabilityResult.getEwsDirectContributor()
            );
            double convlStatusScore = scorer.getConvlAvailabilityScore(
                    availabilityResult.getConvlStatus()
            );

            double statusScore = getGlobalStatusScore(
                    mbStatusScore,
                    triceStatusScore,
                    bnymStatusScore,
                    convlStatusScore
            );

            AIGatewayScoreBreakdown breakdown = new AIGatewayScoreBreakdown(mbStatusScore, bnymStatusScore,
                    triceStatusScore, convlStatusScore);

            return new AIGatewayScore(statusScore, null, breakdown, null);
        }

        // Case: only ownership
        if (inquirySet.equals(Set.of(OWNERSHIP))) {
            double ownershipScore = scorer.getOwnershipScore(
                    matchResult,
                    request.params().getBusinessName()
            );
            return new AIGatewayScore(null, ownershipScore, null, null);
        }

        // Case: both availability and ownership
        if (inquirySet.contains(AVAILABILITY) && inquirySet.contains(OWNERSHIP)) {
            double mbStatusScore = scorer.getMbOnlyAvailabilityScore(
                    availabilityResult.getMbDecision(),
                    availabilityResult.getMbLastSeenMulti(),
                    availabilityResult.getMbReturns()
            );
            double triceStatusScore = scorer.getTriceAvailabilityScore(
                    availabilityResult.getTriceAsvDecision()
            );
            double bnymStatusScore = scorer.getBnymAvailabilityScore(
                    availabilityResult.getBnymStatusCode(),
                    availabilityResult.getEwsDirectContributor()
            );
            double convlStatusScore = scorer.getConvlAvailabilityScore(
                    availabilityResult.getConvlStatus()
            );

            double statusScore = getGlobalStatusScore(
                    mbStatusScore,
                    triceStatusScore,
                    bnymStatusScore,
                    convlStatusScore
            );

            double ownershipScore = scorer.getOwnershipScore(matchResult, request.params().getBusinessName());

            AIGatewayScoreBreakdown breakdown = new AIGatewayScoreBreakdown(mbStatusScore, bnymStatusScore, triceStatusScore, convlStatusScore);
            return new AIGatewayScore(statusScore, ownershipScore, breakdown, null);
        }

        // Default case
        double mbStatusScore = scorer.getMbOnlyAvailabilityScore(
                availabilityResult.getMbDecision(),
                availabilityResult.getMbLastSeenMulti(),
                availabilityResult.getMbReturns()
        );
        double triceStatusScore = scorer.getTriceAvailabilityScore(
                availabilityResult.getTriceAsvDecision()
        );
        double bnymStatusScore = scorer.getBnymAvailabilityScore(
                availabilityResult.getBnymStatusCode(),
                availabilityResult.getEwsDirectContributor()
        );
        double convlStatusScore = scorer.getConvlAvailabilityScore(
                availabilityResult.getConvlStatus()
        );

        AIGatewayScoreBreakdown breakdown = new AIGatewayScoreBreakdown(mbStatusScore, bnymStatusScore, triceStatusScore, convlStatusScore);

        return new AIGatewayScore(null, null, breakdown, null);
    }

    private double getGlobalStatusScore(double mbStatusScore, double triceStatusScore, double bnymStatusScore,
                                        double convlStatusScore) {
        return scorer.getGlobalAvailabilityScoreWithConsortium(
                triceStatusScore,
                bnymStatusScore,
                convlStatusScore,
                mbStatusScore
        );
    }

    private double getGlobalStatusScore(double mbStatusScore, double triceStatusScore, double convlStatusScore) {
        return scorer.getGlobalAvailabilityScoreWithConsortium(
                triceStatusScore,
                0.5,
                convlStatusScore,
                mbStatusScore
        );
    }

    public AIGatewayScore getMbOnlyScores(
            AIGatewayRequest request,
            MbOnlyAvailabilityResult availabilityResult,
            OwnershipResult ownershipResult  // pass null if none
    ) {
        Set<String> inquirySet = updateInquiries(request.payment().getInquiries());

        double mbStatusScore = scorer.getMbOnlyAvailabilityScore(
                availabilityResult.getMbDecision(),
                availabilityResult.getMbLastSeenMulti(),
                availabilityResult.getMbReturns()
        );
        double triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.getTriceAsvDecision());
        double convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.getConvlStatus());

        if (inquirySet.equals(Set.of(AVAILABILITY))) {
            double globalStatusScore = getGlobalStatusScore(mbStatusScore, triceStatusScore, convlStatusScore);

            return new AIGatewayScore(
                    globalStatusScore,
                    null,
                    new AIGatewayScoreBreakdown(mbStatusScore, 0.5, triceStatusScore, convlStatusScore),
                    null
            );
        } else if (inquirySet.equals(Set.of(OWNERSHIP))) {
            double ownershipScore = scorer.getOwnershipScore(
                    Objects.requireNonNull(ownershipResult),
                    request.params().getBusinessName()
            );

            return new AIGatewayScore(
                    null,
                    ownershipScore,
                    null,
                    null
            );
        } else if (inquirySet.contains(AVAILABILITY) && inquirySet.contains(OWNERSHIP)) {
            double globalStatusScore = getGlobalStatusScore(mbStatusScore, triceStatusScore, convlStatusScore);
            double ownershipScore = scorer.getOwnershipScore(
                    Objects.requireNonNull(ownershipResult),
                    request.params().getBusinessName()
            );

            return new AIGatewayScore(
                    globalStatusScore,
                    ownershipScore,
                    new AIGatewayScoreBreakdown(mbStatusScore, 0.5, triceStatusScore, convlStatusScore),
                    null
            );
        } else {
            return new AIGatewayScore(
                    null,
                    null,
                    new AIGatewayScoreBreakdown(mbStatusScore, 0.5, triceStatusScore, convlStatusScore),
                    null
            );
        }
    }
}
