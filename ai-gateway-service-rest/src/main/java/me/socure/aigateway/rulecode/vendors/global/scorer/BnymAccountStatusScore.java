package me.socure.aigateway.rulecode.vendors.global.scorer;

public record BnymAccountStatusScore(String bnymStatusCode, String ewsDirectContributor, double score) {
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof BnymAccountStatusScore other)) return false;
        return safeEqualsIgnoreCase(bnymStatusCode, other.bnymStatusCode) &&
               safeEqualsIgnoreCase(ewsDirectContributor, other.ewsDirectContributor);
    }

    @Override
    public int hashCode() {
        String bnymCode = bnymStatusCode != null ? bnymStatusCode.toLowerCase() : "null";
        String ewsContrib = ewsDirectContributor != null ? ewsDirectContributor.toLowerCase() : "null";
        return (bnymCode + "_" + ewsContrib + "_").hashCode();
    }

    private boolean safeEqualsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equalsIgnoreCase(str2);
    }

    public String getAuditString() {
        return String.format("BNYM Status Code %s, EWS Direct Contributor %s, Score: %f",
                bnymStatusCode, ewsDirectContributor, score);
    }
}
