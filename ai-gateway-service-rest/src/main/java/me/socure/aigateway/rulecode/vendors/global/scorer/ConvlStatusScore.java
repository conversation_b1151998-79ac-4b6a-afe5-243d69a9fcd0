package me.socure.aigateway.rulecode.vendors.global.scorer;

public record ConvlStatusScore(String convlStatus, double score) {
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof ConvlStatusScore other)) return false;
        return safeEqualsIgnoreCase(convlStatus, other.convlStatus);
    }

    @Override
    public int hashCode() {
        String status = convlStatus != null ? convlStatus.toLowerCase() : "null";
        return (status + "_").hashCode();
    }

    private boolean safeEqualsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equalsIgnoreCase(str2);
    }

    public String getAuditString() {
        return String.format("CONVL ASV STATUS %s, Score: %f", convlStatus, score);
    }
}
