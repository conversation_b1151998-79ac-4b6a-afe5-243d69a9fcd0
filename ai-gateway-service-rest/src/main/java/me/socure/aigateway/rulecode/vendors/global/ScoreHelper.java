package me.socure.aigateway.rulecode.vendors.global;

import me.socure.aigateway.model.request.AIGatewayParams;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.rulecode.vendors.global.model.AvailabilityResult;
import me.socure.aigateway.rulecode.vendors.global.model.MbOnlyAvailabilityResult;
import me.socure.aigateway.rulecode.vendors.global.model.OwnershipResult;
import me.socure.aigateway.rulecode.vendors.global.resolution.AddressResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.BusinessNameResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.DobResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.FNameResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.LNameResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.PhoneResolution;
import me.socure.aigateway.rulecode.vendors.global.resolution.SsnResolution;
import me.socure.aigateway.rulecode.vendors.global.scorer.OwnershipScorer;
import me.socure.aigateway.rulecode.vendors.global.util.DateUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static me.socure.aigateway.rulecode.vendors.global.AccountStructureExclusionLoader.getUnknownBankData;
import static me.socure.aigateway.rulecode.vendors.global.ScoreHelperConstants.*;

@Component
public class ScoreHelper {

    private static final Map<String, String> REF_MAP = Map.of(
            MATCH, "Yes",
            WARNING, "No",
            UNKNOWN, "Unknown",
            ALERT, "Conditional",
            CONDITIONAL, "Conditional",
            NO_MATCH, "No",
            "No match", "No"
    );

    public AvailabilityResult processAvailabilityRuleCodes(AIGatewayRequest gatewayRequest, Map<String,
            String> ruleCodes) {

        boolean accountStructureIssueExists = isAccountStructureIssue(gatewayRequest, ruleCodes);
        String bnymStatusCode = imputeStatusCodes(ruleCodes.getOrDefault("BNYVL.100001", "UNKNOWN"));
        String mbDecision = ruleCodes.getOrDefault("MBTVL.100004", "U");
        String mbLastSeen = ruleCodes.getOrDefault("MBTVL.100005", "N");
        String mbReturns = ruleCodes.getOrDefault("MBTVL.100010", "N");
        String mbLastSeenMulti = ruleCodes.getOrDefault("MBTVL.100011", "N");
        String triceAsvDecision = ruleCodes.getOrDefault("TRICE.100005", "unknown");
        String convlStatus = ruleCodes.getOrDefault("CONVL.100002", "unknown");
        String ewsDirectContributor = ruleCodes.get("BNYVL.100020");

        String asvPattern = String.join("_", bnymStatusCode, mbDecision, mbLastSeen, ewsDirectContributor);
        String maxLastSeenDate = maxLastSeen(ruleCodes);

        // CON-422: Overwrite account structure decision code for the score lookup
        String overrideMbDecision = (!accountStructureIssueExists && "AS".equalsIgnoreCase(mbDecision)) ? "U" : mbDecision;

//        txnLogger.info("[ScorerHelper] Availability Returns {}, {}, {}, {}", bnymStatusCode, mbDecision, mbLastSeen, ewsDirectContributor);

        return new AvailabilityResult(
                bnymStatusCode,
                overrideMbDecision,
                mbLastSeen,
                mbReturns,
                mbLastSeenMulti,
                ewsDirectContributor,
                asvPattern,
                accountStructureIssueExists,
                triceAsvDecision,
                convlStatus,
                maxLastSeenDate
        );
    }

    private boolean isAccountStructureIssue(AIGatewayRequest gatewayRequest, Map<String, String> ruleCodeMap) {
        String bnyStatusCode = ruleCodeMap.get("BNYVL.100001");
        String propertyMessageSimp = ruleCodeMap.getOrDefault("MBTVL.100009", "");
        String triceAsvDecision = ruleCodeMap.get("TRICE.100005");

        boolean accountStructureIssueExists = ACCOUNT_STRUCTURE.equalsIgnoreCase(propertyMessageSimp);

        boolean isBankDataUnknown = getUnknownBankData().stream()
                .anyMatch(data ->
                        gatewayRequest.payment().getRoutingNumber().equalsIgnoreCase(data.getRtn()) &&
                                gatewayRequest.payment().getAccountNumber().startsWith(data.getAccountPrefix())
                );

        if (isBankDataUnknown) {
            return false;
        }
        if (VALID.equalsIgnoreCase(bnyStatusCode) || INVALID.equalsIgnoreCase(bnyStatusCode)) {
            return false;
        }
        if (TRICE_ASV_DECISION_ACCEPTED.equalsIgnoreCase(triceAsvDecision)) {
            return false;
        }

        return accountStructureIssueExists;
    }

    private String imputeStatusCodes(String value) {
        return value.replace("ERROR", "UNKNOWN");
    }

    private String maxLastSeen(Map<String, String> ruleCodeMap) {
        Set<String> dates = new HashSet<>(List.of(
                ruleCodeMap.getOrDefault("TRICE.100006", ""),
                ruleCodeMap.getOrDefault("MBTVL.100012", ""),
                ruleCodeMap.getOrDefault("BNYVL.100125", "")
        ));
        return DateUtils.findMaxDateAsString(dates);
    }

    public OwnershipResult processRuleCodes(AIGatewayRequest gatewayRequest, Map<String, String> cRuleCodes, Map<String,
            Double> nRuleCodes) {
        String businessName = gatewayRequest.params().getBusinessName();
        if (businessName == null || businessName.isEmpty()) {
            return processPersonalRuleCodes(cRuleCodes, nRuleCodes);
        } else {
            return processBusinessRuleCodes(gatewayRequest, cRuleCodes);
        }
    }

    private OwnershipResult processPersonalRuleCodes(Map<String, String> cRuleCodes, Map<String, Double> nRuleCodes) {
        String ssn = Optional.ofNullable(cRuleCodes.get("SOCVL.100003"))
                .filter(match -> !match.isEmpty())
                .orElse(UNKNOWN);

        String dob = Optional.ofNullable(cRuleCodes.get("SOCVL.100004"))
                .filter(match -> !match.isEmpty())
                .orElse(UNKNOWN);

        String saiFirstNameMatch = fNameMatch(cRuleCodes);
        String saiLastNameMatch = lNameMatch(cRuleCodes);
        String saiSsnMatch = ssnMatch(cRuleCodes, nRuleCodes);
        String saiDobMatch = dobMatch(cRuleCodes, nRuleCodes);
        String socIdSsn = socIdPiiMatch(cRuleCodes, nRuleCodes, ssn);
        String socIdDob = socIdPiiMatch(cRuleCodes, nRuleCodes, dob);
        String saiPhoneMatch = phoneMatch(cRuleCodes);
        String saiAddressMatch = addressMatch(cRuleCodes);
        String maxLastSeenDate = maxLastSeen(cRuleCodes);

        String aovPattern = String.join("_",
                OwnershipScorer.nameMatchScoringMap.get(saiFirstNameMatch),
                OwnershipScorer.nameMatchScoringMap.get(saiLastNameMatch),
                saiSsnMatch,
                saiDobMatch,
                saiPhoneMatch,
                saiAddressMatch);

        return OwnershipResult.builder()
                .firstNameMatch(saiFirstNameMatch)
                .lastNameMatch(saiLastNameMatch)
                .ssnMatch(saiSsnMatch)
                .dobMatch(saiDobMatch)
                .socIdSsnMatch(socIdSsn)
                .socIdDobMatch(socIdDob)
                .phoneMatch(saiPhoneMatch)
                .addressMatch(saiAddressMatch)
                .aovPattern(aovPattern)
                .busNameMatch(UNKNOWN)
                .einMatch(UNKNOWN)
                .phoneMatchBus(UNKNOWN)
                .addressMatchBus(UNKNOWN)
                .maxLastSeenDate(maxLastSeenDate)
                .build();
    }

    private OwnershipResult processBusinessRuleCodes(AIGatewayRequest gatewayRequest, Map<String, String> ruleCodes) {
        String saiEinMatch = einMatch(gatewayRequest, ruleCodes);
        String saiBusNameMatch = busNameMatch(ruleCodes);
        String saiPhoneMatchBus = phoneMatchBus(gatewayRequest, ruleCodes);
        String saiAddressMatchBus = addressMatchBus(gatewayRequest, ruleCodes);
        String maxLastSeenDate = maxLastSeen(ruleCodes);

        String aovPattern = String.join("_",
                saiBusNameMatch,
                saiEinMatch,
                saiPhoneMatchBus,
                saiAddressMatchBus);

        return OwnershipResult.builder()
                .firstNameMatch(UNKNOWN)
                .lastNameMatch(UNKNOWN)
                .ssnMatch(UNKNOWN)
                .dobMatch(UNKNOWN)
                .socIdSsnMatch(UNKNOWN)
                .socIdDobMatch(UNKNOWN)
                .phoneMatch(saiPhoneMatchBus)
                .addressMatch(UNKNOWN)
                .aovPattern(aovPattern)
                .busNameMatch(saiBusNameMatch)
                .einMatch(saiEinMatch)
                .phoneMatchBus(saiPhoneMatchBus)
                .addressMatchBus(saiAddressMatchBus)
                .maxLastSeenDate(maxLastSeenDate)
                .build();
    }

    private String fNameMatch(Map<String, String> cRuleCodes) {
        String mbtFirstNameMatch = cRuleCodes.getOrDefault("MBTVL.100001", UNKNOWN);
        if ("".equals(mbtFirstNameMatch)) {
            mbtFirstNameMatch = UNKNOWN;
        }

        return FNameResolution.nameMatch(
                cRuleCodes.getOrDefault("CONVL.101001", UNKNOWN),
                cRuleCodes.getOrDefault("BNYVL.100016", UNKNOWN),
                mbtFirstNameMatch
        );
    }

    private String lNameMatch(Map<String, String> cRuleCodes) {
        String mbtLastNameMatch = cRuleCodes.getOrDefault("MBTVL.100002", UNKNOWN);
        if ("".equals(mbtLastNameMatch)) {
            mbtLastNameMatch = UNKNOWN;
        }

        return LNameResolution.nameMatch(
                cRuleCodes.getOrDefault("CONVL.101002", UNKNOWN),
                cRuleCodes.getOrDefault("BNYVL.100017", UNKNOWN),
                mbtLastNameMatch
        );
    }

    private String ssnMatch(Map<String, String> cRuleCodes, Map<String, Double> nRuleCodes) {
        String ssn = cRuleCodes.getOrDefault("SOCVL.100003", UNKNOWN);
        if (ssn == null || ssn.isEmpty()) {
            ssn = UNKNOWN;
        }
        String ssnResult = socIdPiiMatch(cRuleCodes, nRuleCodes, ssn);

        return SsnResolution.ssnMatch(
                cRuleCodes.getOrDefault("CONVL.101003", UNKNOWN),
                cRuleCodes.getOrDefault("BNYVL.100008", UNKNOWN),
                ssnResult
        );
    }

    private String dobMatch(Map<String, String> cRuleCodes, Map<String, Double> nRuleCodes) {
        String dob = cRuleCodes.getOrDefault("SOCVL.100004", UNKNOWN);
        if (dob.isEmpty()) {
            dob = UNKNOWN;
        }
        String dobResult = socIdPiiMatch(cRuleCodes, nRuleCodes, dob);

        return DobResolution.dobMatch(
                cRuleCodes.getOrDefault("CONVL.101004", UNKNOWN),
                cRuleCodes.getOrDefault("BNYVL.100009", UNKNOWN),
                dobResult
        );
    }

    private String socIdPiiMatch(Map<String, String> cRuleCodeMap,
                                 Map<String, Double> nRuleCodeMap,
                                 String piiElement) {
        boolean nameThreshold = isNameThresholdMatch(cRuleCodeMap, nRuleCodeMap);
        return nameThreshold
                ? REF_MAP.getOrDefault(piiElement, UNKNOWN)
                : UNKNOWN;
    }

    private boolean isNameThresholdMatch(Map<String, String> cRuleCodeMap,
                                         Map<String, Double> nRuleCodeMap) {
        String saiFnameMatch = fNameMatch(cRuleCodeMap);
        String saiLnameMatch = lNameMatch(cRuleCodeMap);

        String socIdFnameFuzzy = cRuleCodeMap.getOrDefault("SOCVL.100001", UNKNOWN);
        if (socIdFnameFuzzy.isEmpty()) {
            socIdFnameFuzzy = UNKNOWN;
        }

        String socIdLnameFuzzy = cRuleCodeMap.getOrDefault("SOCVL.100002", UNKNOWN);
        if (socIdLnameFuzzy.isEmpty()) {
            socIdLnameFuzzy = UNKNOWN;
        }

        double socIdMatchScore = nRuleCodeMap.getOrDefault("SOCVL.100005", 0.0);

        boolean socIdFuzzyMatch = MATCH.equals(socIdFnameFuzzy) && MATCH.equals(socIdLnameFuzzy);
        boolean saiNameMatch = YES.equals(saiFnameMatch) && YES.equals(saiLnameMatch);
        boolean isOverThreshold = socIdMatchScore > SOCURE_ID_SCORE_THRESHOLD;

        return isOverThreshold && socIdFuzzyMatch && saiNameMatch;
    }

    private String phoneMatch(Map<String, String> ruleCodeMap) {
        String mbtBusNameMatch = ruleCodeMap.getOrDefault("MBTVL.100003", UNKNOWN);
        if (mbtBusNameMatch == null || mbtBusNameMatch.isEmpty()) {
            mbtBusNameMatch = UNKNOWN;
        }

        return PhoneResolution.phoneMatch(
                ruleCodeMap.getOrDefault("CONVL.101005", UNKNOWN),
                ruleCodeMap.getOrDefault("BNYVL.100022", UNKNOWN),
                mbtBusNameMatch
        );
    }

    private String addressMatch(Map<String, String> ruleCodeMap) {
        String consAddress = ruleCodeMap.getOrDefault("CONVL.101007", UNKNOWN);
        if (consAddress.isEmpty()) {
            consAddress = UNKNOWN;
        }

        return AddressResolution.addressMatch(
                consAddress,
                ruleCodeMap.getOrDefault("BNYVL.100018", UNKNOWN)
        );
    }

    private String einMatch(AIGatewayRequest gatewayRequest, Map<String, String> ruleCodeMap) {
        String businessEin = gatewayRequest.params().getBusinessEin();
        if (businessEin == null) {
            return NOT_PROVIDED;
        } else {
            String code = ruleCodeMap.getOrDefault("BNYVL.100008", UNKNOWN);
            return REF_MAP.getOrDefault(code, UNKNOWN);
        }
    }

    private String busNameMatch(Map<String, String> ruleCodeMap) {
        String mbtBusNameMatch = ruleCodeMap.getOrDefault("MBTVL.100001", UNKNOWN);
        if ("".equals(mbtBusNameMatch)) {
            mbtBusNameMatch = UNKNOWN;
        }

        return BusinessNameResolution.busNameMatch(
                ruleCodeMap.getOrDefault("BNYVL.100007", UNKNOWN),
                mbtBusNameMatch
        );
    }


    private String phoneMatchBus(AIGatewayRequest gatewayRequest, Map<String, String> cRuleCodeMap) {
        AIGatewayParams params = gatewayRequest.params();
        String mobileNumber = params.getMobileNumber();
        String businessPhone = params.getBusinessPhone();

        boolean mobileEmpty = (mobileNumber == null || mobileNumber.isEmpty());
        boolean businessEmpty = (businessPhone == null || businessPhone.isEmpty());

        if (mobileEmpty && businessEmpty) {
            return NOT_PROVIDED;
        } else {
            return phoneMatch(cRuleCodeMap);
        }
    }

    private String addressMatchBus(AIGatewayRequest gatewayRequest, Map<String, String> ruleCodeMap) {
        String physicalAddress = gatewayRequest.params().getPhysicalAddress();
        if (physicalAddress == null) {
            return NOT_PROVIDED;
        } else {
            return addressMatch(ruleCodeMap);
        }
    }

    public MbOnlyAvailabilityResult processMbOnlyAvailabilityRuleCodes(AIGatewayRequest gatewayRequest,
                                                                       Map<String, String> ruleCodes) {
        String mbDecision = ruleCodes.getOrDefault("MBTVL.100004", "U");
        String mbLastSeenMulti = ruleCodes.getOrDefault("MBTVL.100011", "N");
        String mbReturns = ruleCodes.getOrDefault("MBTVL.100010", "No");

        String triceAsvDecision = ruleCodes.getOrDefault("TRICE.100005", "unknown");
        String convlStatus = ruleCodes.getOrDefault("CONVL.100002", "unknown");

        boolean accountStructureIssue = isMbAccountStructureIssue(gatewayRequest, ruleCodes);
        String overrideMbDecision = (!accountStructureIssue && "AS".equalsIgnoreCase(mbDecision)) ? "U" : mbDecision;

        String asvPattern = String.join("_",
                overrideMbDecision,
                mbLastSeenMulti,
                mbReturns
        );

        String maxLastSeenDate = maxLastSeen(ruleCodes);

        return new MbOnlyAvailabilityResult(
                overrideMbDecision,
                mbLastSeenMulti,
                mbReturns,
                asvPattern,
                accountStructureIssue,
                triceAsvDecision,
                convlStatus,
                maxLastSeenDate
        );
    }

    private boolean isMbAccountStructureIssue(AIGatewayRequest gatewayRequest, Map<String, String> ruleCodeMap) {
        String propertyMessageSimp = ruleCodeMap.getOrDefault("MBTVL.100009", "");
        String triceDecision = ruleCodeMap.getOrDefault("TRICE.100005", "");

        boolean isBankDataUnknown = AccountStructureExclusionLoader
                .getUnknownBankData().stream()
                .anyMatch(data ->
                        gatewayRequest.payment().getRoutingNumber()
                                .equalsIgnoreCase(data.getRtn()) &&
                                gatewayRequest.payment().getAccountNumber()
                                        .startsWith(data.getAccountPrefix())
                );

        boolean accountStructureIssueExists = ACCOUNT_STRUCTURE.equalsIgnoreCase(propertyMessageSimp);

        if (isBankDataUnknown) {
            return false;
        }
        if (TRICE_ASV_DECISION_ACCEPTED.equalsIgnoreCase(triceDecision)) {
            return false;
        }
        return accountStructureIssueExists;
    }


}
