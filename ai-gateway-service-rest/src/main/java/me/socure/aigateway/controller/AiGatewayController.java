package me.socure.aigateway.controller;

import jakarta.validation.Valid;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.response.AiGatewayResponseWrapper;
import me.socure.aigateway.service.AiGatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/account-validation")
public class AiGatewayController {

    private final AiGatewayService aiGatewayService;

    @Autowired
    public AiGatewayController(final AiGatewayService aiGatewayService) {
        this.aiGatewayService = aiGatewayService;
    }

    /**
     * Handles the POST request for processing SAI information.
     *
     * @param request The request body containing the account intelligence details.
     * @return CompletableFuture wrapping the ResponseEntity of AIGatewayResponse.
     */
    @PostMapping
    public CompletableFuture<ResponseEntity<AiGatewayResponseWrapper>> process(
            @Valid
            @RequestBody final AIGatewayRequest request) {
        return aiGatewayService.processRequestAsync(request)
                .thenApply(ResponseEntity::ok);
    }
}
