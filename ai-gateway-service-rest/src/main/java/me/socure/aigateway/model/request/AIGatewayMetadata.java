package me.socure.aigateway.model.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public final class AIGatewayMetadata {
    private long accountId;
    private int environmentTypeId;
    private boolean maskPii;
    @NotNull(message = "Transaction ID is required")
    private String transactionId;
    private String memo;
    private String ultimateSendingPartyId;
    private long submissionDate;
}