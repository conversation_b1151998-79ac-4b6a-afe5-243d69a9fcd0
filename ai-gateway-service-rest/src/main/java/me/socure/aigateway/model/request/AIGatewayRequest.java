package me.socure.aigateway.model.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.Pii;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Builder
public record AIGatewayRequest(
        @Valid
        @NotNull(message = "Metadata is required")
        AIGatewayMetadata metadata,
        @Valid
        @NotNull(message = "Payment details are required")
        AIGatewayPayment payment,
        AIGatewayParams params,
        @NotEmpty(message = "Atleast one vendor is required")
        List<AIGatewayVendorConfig> vendors) {

    public AIVendorRequest toVendorRequest() {
        return AIVendorRequest.builder()
                .transactionId(metadata.getTransactionId())
                .accountId(String.valueOf(metadata.getAccountId()))
                .piis(new Pii(
                        params.getFirstName(),
                        params.getSurName(),
                        params.getBusinessName(),
                        params.getDob(),
                        payment.getAccountNumber(),
                        payment.getRoutingNumber(),
                        payment.getAccountCountry(),
                        params.getPhysicalAddress(),
                        params.getPhysicalAddress2(),
                        params.getCity(),
                        params.getState(),
                        params.getCountry(),
                        params.getZip(),
                        resolvePhoneValue(),
                        params.getNationalId(),
                        params.getEmail()
                ))
                .vendors(List.of())
                .vendorConfigs(Map.of())
                .maskPiiEnabled(metadata.isMaskPii())
                .submissionDate(metadata().getSubmissionDate())
                .build();
    }

    public String resolvePhoneValue() {
        String phoneVal = params.getMobileNumber();
        if (params.getBusinessName() != null && !params.getBusinessName().isEmpty() &&
                params.getBusinessPhone() != null && !params.getBusinessPhone().isEmpty()) {
            phoneVal = params.getBusinessPhone();
        }
        return phoneVal == null || phoneFilterSet.contains(phoneVal) ? null:phoneVal;
    }

    private static final Set<String> phoneFilterSet = Set.of("***********", "***********");

}
