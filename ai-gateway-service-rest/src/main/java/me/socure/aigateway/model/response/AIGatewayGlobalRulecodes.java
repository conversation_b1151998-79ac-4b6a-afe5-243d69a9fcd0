package me.socure.aigateway.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIGatewayGlobalRulecodes {
    @Builder.Default
    private String name = AIGatewayGlobalRulecodes.GLOBAL; // Default value for `name`
    private Map<String, Double> numericalRulecodes; // Map of String to Double
    private Map<String, String> categoricalRulecodes; // Map of String to String

    public static final String GLOBAL = "GLOBAL"; // Static constant for `global`
}
