package me.socure.aigateway.model.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIGatewayPayment {
    @NotNull(message = "Account number is required")
    private String accountNumber;
    @NotNull(message = "Routing number is required")
    private String routingNumber;
    private String accountCountry;
    private Set<String> inquiries;
}
