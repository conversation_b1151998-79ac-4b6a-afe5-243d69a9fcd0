package me.socure.aigateway.util;

import lombok.extern.log4j.Log4j2;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Optional;
import java.util.zip.GZIPInputStream;

@Log4j2
public class GzipUtil {
    public static Optional<String> decompress(byte[] compressed) {
        try (GZIPInputStream gis = new GZIPInputStream(new ByteArrayInputStream(compressed));
             InputStreamReader isr = new InputStreamReader(gis);
             BufferedReader reader = new BufferedReader(isr)) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return Optional.of(sb.toString());
        } catch (IOException e) {
            log.error("Error while decompressing GZIP data", e);
            return Optional.empty();
        }
    }
}
