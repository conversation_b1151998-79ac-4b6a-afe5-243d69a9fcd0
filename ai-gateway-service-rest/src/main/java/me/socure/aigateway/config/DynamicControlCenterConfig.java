package me.socure.aigateway.config;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory;
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import scala.concurrent.ExecutionContext;

import java.util.HashMap;
import java.util.Map;

@Getter
@Configuration
@ConfigurationProperties(prefix = "dynamic.control.center")
public class DynamicControlCenterConfig {

    @Autowired
    private ExecutionContext executionContext;

    @Getter
    @Setter
    private S3 s3 = new S3();

    @Getter
    @Setter
    private Memcached memcached = new Memcached();

    @Getter
    @Setter
    private LocalCache localCache = new LocalCache();  // Initialize with default value

    @Getter
    @Setter
    public static class S3 {
        private String bucketName;
    }

    public static class Memcached {
        @Getter
        @Setter
        private String host;
        @Getter
        @Setter
        private int port;
        @Getter
        @Setter
        private long ttl;
    }

    @Getter
    @Setter
    public static class LocalCache {
        private Timeout timeout = new Timeout();  // Initialize with default value

        @Getter
        @Setter
        public static class Timeout {
            private int minutes = 5;  // Provide a default value to avoid null
        }
    }

    @Bean
    public DynamicControlCenterV2Evaluate dynamicControlCenterV2Evaluate() {
        return DynamicControlCenterV2Factory.getEvaluator(createConfig(), executionContext);
    }

    private Config createConfig() {
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("dynamic.control.center.s3.bucketName", s3.getBucketName());
        configMap.put("dynamic.control.center.memcached.host", memcached.getHost());
        configMap.put("dynamic.control.center.memcached.port", memcached.getPort());
        configMap.put("dynamic.control.center.memcached.ttl", memcached.getTtl());
        configMap.put("dynamic.control.center.local.cache.timeout.minutes", localCache.getTimeout().getMinutes());
        return ConfigFactory.parseMap(configMap);
    }
}
