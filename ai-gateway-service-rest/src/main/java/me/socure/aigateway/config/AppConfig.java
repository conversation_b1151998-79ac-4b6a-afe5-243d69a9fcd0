package me.socure.aigateway.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;

import lombok.Getter;
import me.socure.aigateway.config.factory.TPAuditClientFactory;
import me.socure.aigateway.config.properties.TPAuditClientConfig;
import me.socure.service.audit.client.TPAuditClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import scala.concurrent.ExecutionContext;
import scala.concurrent.JavaConversions;

@Configuration
@Getter
public class AppConfig {
    @Value("${dynamoDB.table.name}")
    private String tableName;

    @Value("${dynamoDB.table.config-key}")
    private String configKey;

    @Value("${dynamoDB.table.lookup-key}")
    private String tableLookupKey;

    @Value("${executioncontext.maxPoolSize}")
    private int maxPoolSize;

    @Value("${executioncontext.corePoolSize}")
    private int corePoolSize;

    @Value("${executioncontext.queueSize}")
    private int queueSize;

    @Value("${executioncontext.threadNamePrefix}")
    private String threadNamePrefix;

    @Value("${scheduler.poolSize}")
    private int scheduledThreadPoolSize;
    @Value("${scheduler.threadNamePrefix}")
    private String scheduledThreadNamePrefix;

    @Bean
    public Executor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueSize);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.initialize();
        return executor;
    }

    @Bean
    public ExecutionContext getExecutionContext(Executor executor) {
        return JavaConversions.asExecutionContext(executor);
    }

    @Bean
    public ScheduledExecutorService getSchedulerExecutorService(){
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(scheduledThreadPoolSize);
        scheduler.setThreadNamePrefix(scheduledThreadNamePrefix);
        scheduler.setRemoveOnCancelPolicy(true);
        scheduler.initialize();
        return scheduler.getScheduledExecutor();
    }

    @Bean
    public TPAuditClient getTPAuditClient(final ThirdPartyAuditingSqsConfig thirdPartyAuditingSqsConfig) {
        TPAuditClientConfig config = TPAuditClientConfig.builder()
                .threadPoolSize(thirdPartyAuditingSqsConfig.getThreadPoolSize())
                .maxRetries(thirdPartyAuditingSqsConfig.getMaxRetries())
                .primarySqsRegion(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsRegion())
                .primarySqsThirdPartyQueueName(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsThirdPartyQueueName())
                .primarySqsWaitTimeSeconds(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsWaitTimeSeconds())
                .primarySqsMaxBatchSize(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsMaxBatchSize())
                .primarySqsMaxBufferSize(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsMaxBufferSize())
                .primarySqsThirdPartyMaxInFlight(thirdPartyAuditingSqsConfig.primaryThirdPartyAuditingSqsProperties().getSqsThirdPartyMaxInFlight())
                .fallback0SqsRegion(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsRegion())
                .fallback0SqsThirdPartyQueueName(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsThirdPartyQueueName())
                .fallback0SqsWaitTimeSeconds(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsWaitTimeSeconds())
                .fallback0SqsMaxBatchSize(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsMaxBatchSize())
                .fallback0SqsMaxBufferSize(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsMaxBufferSize())
                .fallback0SqsThirdPartyMaxInFlight(thirdPartyAuditingSqsConfig.fallback0ThirdPartyAuditingSqsProperties().getSqsThirdPartyMaxInFlight())
                .fallback1SqsRegion(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsRegion())
                .fallback1SqsThirdPartyQueueName(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsThirdPartyQueueName())
                .fallback1SqsWaitTimeSeconds(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsWaitTimeSeconds())
                .fallback1SqsMaxBatchSize(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsMaxBatchSize())
                .fallback1SqsMaxBufferSize(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsMaxBufferSize())
                .fallback1SqsThirdPartyMaxInFlight(thirdPartyAuditingSqsConfig.fallback1ThirdPartyAuditingSqsProperties().getSqsThirdPartyMaxInFlight())
                .s3LargeFilesBucket(thirdPartyAuditingSqsConfig.getS3LargeFilesBucket())
                .s3ThirdPartyRegion(thirdPartyAuditingSqsConfig.getS3ThirdPartyRegion())
                .s3ThirdPartyBucket(thirdPartyAuditingSqsConfig.getS3ThirdPartyBucket())
                .sqsMinBackOff(thirdPartyAuditingSqsConfig.getSqsMinBackOff())
                .sqsMaxBackOff(thirdPartyAuditingSqsConfig.getSqsMaxBackOff())
                .build();
        return TPAuditClientFactory.get(config);
    }
}

