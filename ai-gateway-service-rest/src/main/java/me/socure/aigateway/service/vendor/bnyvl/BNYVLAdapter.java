package me.socure.aigateway.service.vendor.bnyvl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.RequestUtil;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.Pii;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.model.vendor.*;
import me.socure.aigateway.rulecode.RulecodeHelper;
import me.socure.aigateway.service.vendor.CallType;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorConfigFactory;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.service.vendor.bnyvl.model.BnyMellonResponse;
import me.socure.aigateway.service.vendor.bnyvl.model.Data;
import me.socure.aigateway.service.vendor.bnyvl.model.Metadata;
import me.socure.aigateway.service.vendor.bnyvl.model.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Log4j2
public class BNYVLAdapter implements VendorAdapter {
    private final VendorConfigFactory vendorConfigFactory;
    private final ObjectMapper objectMapper;

    private final CoverageRoutingNumberReader coverageRoutingNumberReader;
    String vendorName = "bnyvl";

    private final RulecodeHelper rulecodeHelper;

    @Autowired
    public BNYVLAdapter(final VendorConfigFactory vendorConfigFactory, ObjectMapper objectMapper, RulecodeHelper rulecodeHelper,
                        CoverageRoutingNumberReader coverageRoutingNumberReader){
        this.vendorConfigFactory  = vendorConfigFactory;
        this.objectMapper = objectMapper;
        this.rulecodeHelper = rulecodeHelper;
        this.coverageRoutingNumberReader = coverageRoutingNumberReader;
    }
    @Override
    public CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        return CallType.builder().isHttpCallAllowed(true).build();
    }

    @Override
    public VendorConfig populateVendorConfig(AIGatewayRequest req) {
        String inquires = RequestUtil.getInquiries(req.payment().getInquiries());
        VendorConfig vendorConfig = vendorConfigFactory.getVendorConfig(vendorName);
        vendorConfig.setInquiries(inquires);
        return vendorConfig;
    }

    @Override
    public VendorParsedResponse<BnyMellonResponse> processResponse(VendorRawResponse vendorResponse) {
        if(!HttpStatusCode.valueOf(vendorResponse.getStatus()).is2xxSuccessful()) {
            return VendorParsedResponse.<BnyMellonResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(null)
                    .build();
        }
        try {
            BnyMellonResponse resp = objectMapper.readValue(vendorResponse.getResponse().toString(), BnyMellonResponse.class);
            return VendorParsedResponse.<BnyMellonResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(resp)
                    .build();
        } catch (Exception e){
            log.error("Error parsing BNY Mellon response: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public Data preProcessRuleCodes(VendorParsedResponse vendorParsedResponse, AIVendorRequest aiVendorRequest) {
        if (vendorParsedResponse == null) {
            return null;
        }

        BnyMellonResponse response = (BnyMellonResponse) vendorParsedResponse.getParsedData();
        if (response == null || response.getResult() == null) {
            return null;
        }

        Data[] data = response.getResult().getData();
        if (data == null || data.length == 0) {
            return null;
        }

        return data[0];
    }


    @Override
    public  <T>AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T data) {
        if(vendorResponse==null||vendorResponse.getParsedData()==null)
            return AIVendorRuleCodes.builder().build();
        AIVendorRuleCodes resp = rulecodeHelper.executeVendorFunctionCommon(vendorName, aiVendorRequest, vendorResponse, data, false, null);
        return rulecodeHelper.executeAndCombineDerivedRuleCode(vendorName, aiVendorRequest, vendorResponse, data, resp);
    }

   @Override
    public AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp) {
        BnyMellonResponse bnyResp = (BnyMellonResponse) vendorResponse.getParsedData();
        Data data = preProcessRuleCodes(vendorResponse, aiVendorRequest);

        String ewsContri = Optional.ofNullable(aiVendorRequest)
                .map(AIVendorRequest::getPiis)
                .map(Pii::getRoutingNumber)
                .map(rn -> coverageRoutingNumberReader.getRoutingNumbers().contains(rn) ? "Y" : "N")
                .orElse("N");

        AIVendorMetadata metadata = AIVendorMetadata.builder()
                .name("BNYMELLON")
                .success(Optional.ofNullable(bnyResp)
                        .map(BnyMellonResponse::getMetadata)
                        .map(Metadata::isSuccess)
                        .orElse(false))
                .statusCode(Optional.ofNullable(data).map(Data::getRequestStatus).orElse(null))
                .vendorReferenceId(Optional.ofNullable(data).map(Data::getTransactionId).orElse(null))
                .transactionId(Optional.ofNullable(data).map(Data::getClientReferenceId).orElse(null))
                .build();

        AIVendorError aiVendorError = Optional.ofNullable(bnyResp)
                .map(BnyMellonResponse::getError)
                .map(err -> AIVendorError.builder()
                        .code(err.getCode())
                        .description(err.getDescription())
                        .build())
                .orElse(null);

        AccountOwnership ownership = Optional.ofNullable(data)
                .map(Data::getStatus)
                .map(Status::getAoa)
                .map(aoa -> AccountOwnership.builder()
                        .code(aoa.getStatusCode())
                        .description(aoa.getDescription())
                        .build())
                .orElse(null);

        AccountStatus status = Optional.ofNullable(data)
                .map(Data::getStatus)
                .map(Status::getAsv)
                .map(asv -> AccountStatus.builder()
                        .code(asv.getStatusCode())
                        .description(asv.getDescription())
                        .ewsDirectContributor(ewsContri)
                        .build())
                .orElse(null);

        return AiResponseData.builder()
                .metadata(metadata)
                .error(aiVendorError)
                .result(AIVendorResult.builder()
                        .ownership(ownership)
                        .status(status)
                        .build())
                .rulecodes(ruleCodeResp)
                .build();
    }

}
