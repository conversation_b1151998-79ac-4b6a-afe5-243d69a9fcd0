package me.socure.aigateway.service.vendor.mbtvl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.model.vendor.AIVendorMetadata;
import me.socure.aigateway.model.vendor.AIVendorResult;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;
import me.socure.aigateway.rulecode.RulecodeHelper;
import me.socure.aigateway.service.vendor.CallType;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorConfigFactory;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.service.vendor.mbtvl.model.*;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Log4j2
public class MBTVLAdapter implements VendorAdapter {

    String vendorName = "mbtvl";
    private final VendorConfigFactory vendorConfigFactory;
    private final ObjectMapper objectMapper;
    private final RulecodeHelper rulecodeHelper;

    public MBTVLAdapter(final VendorConfigFactory vendorConfigFactory, final ObjectMapper objectMapper, final RulecodeHelper rulecodeHelper) {
        this.vendorConfigFactory = vendorConfigFactory;
        this.objectMapper = objectMapper;
        this.rulecodeHelper = rulecodeHelper;
    }

    @Override
    public CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        return CallType.builder().isHttpCallAllowed(true).build();
    }


    @Override
    public VendorConfig populateVendorConfig(AIGatewayRequest aiGatewayRequest) {
        return vendorConfigFactory.getVendorConfig(vendorName);
    }

    @Override
    public VendorParsedResponse processResponse(VendorRawResponse vendorResponse) {
        if (!HttpStatusCode.valueOf(vendorResponse.getStatus()).is2xxSuccessful()) {
            return VendorParsedResponse.<MicrobiltResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(null)
                    .build();
        }
        try {
            MicrobiltResponse resp = objectMapper.readValue(vendorResponse.getResponse().toString(), MicrobiltResponse.class);
            return VendorParsedResponse.<MicrobiltResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(resp)
                    .build();
        } catch (Exception e) {
            log.error("Error parsing MBTVL response: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public NameValue[] preProcessRuleCodes(VendorParsedResponse vendorParsedResponse, AIVendorRequest aiVendorRequest) {
        return Optional.ofNullable(vendorParsedResponse)
                .map(VendorParsedResponse::getParsedData)
                .filter(MicrobiltResponse.class::isInstance)
                .map(MicrobiltResponse.class::cast)
                .map(MicrobiltResponse::getResponse)
                .map(Response::getContent)
                .map(Content::getDecision)
                .map(Decision::getProperties)
                .orElse(new NameValue[0]);
    }

    @Override
    public <T> AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T preProcessRuleCodes) {
        if (vendorResponse == null || vendorResponse.getParsedData() == null)
            return AIVendorRuleCodes.builder().build();
        AIVendorRuleCodes resp = rulecodeHelper.executeVendorFunctionCommon(vendorName, aiVendorRequest, vendorResponse, preProcessRuleCodes, false, null);
        return rulecodeHelper.executeAndCombineDerivedRuleCode(vendorName, aiVendorRequest, vendorResponse, preProcessRuleCodes, resp);
    }

    @Override
    public AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp) {
        MicrobiltResponse microResp = Optional.ofNullable(vendorResponse)
                .map(VendorParsedResponse::getParsedData)
                .filter(MicrobiltResponse.class::isInstance)
                .map(MicrobiltResponse.class::cast).orElse(null);

        String metaDataStatusCode = Optional.ofNullable(microResp)
                .map(MicrobiltResponse::getMsgRsHdr)
                .map(MessageHeader::getStatus).map(HeaderStatus::getStatusCode)
                .filter("0"::equals).map(code -> "COMPLETED").orElse("FAILED");
        boolean metaDataSuccess = "COMPLETED".equals(metaDataStatusCode);
        String vendorRefId = Optional.ofNullable(microResp)
                .map(MicrobiltResponse::getMsgRsHdr)
                .map(MessageHeader::getRqUID).map(reqId -> removeBraces(reqId))
                .orElse(null);

        AIVendorMetadata aiVendorMetadata = AIVendorMetadata.builder()
                .name("MICROBILT").success(metaDataSuccess)
                .statusCode(metaDataStatusCode).vendorReferenceId(vendorRefId)
                .transactionId(aiVendorRequest.getTransactionId()).build();

        return AiResponseData.builder().metadata(aiVendorMetadata)
                .result(AIVendorResult.builder().build())
                .rulecodes(ruleCodeResp)
                .build();
    }

    public static String removeBraces(String fieldVal) {
        Pattern pattern = Pattern.compile("\\{(.*?)}");
        Matcher matcher = pattern.matcher(fieldVal);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new IllegalArgumentException("No matching braces found in input: " + fieldVal);
    }

}