package me.socure.aigateway.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.constants.Flags;
import me.socure.aigateway.exception.exceptions.BadRequestException;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIGatewayVendorConfig;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.*;
import me.socure.aigateway.model.vendor.AIVendorMetadata;
import me.socure.aigateway.model.vendor.AIVendorResult;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;
import me.socure.aigateway.reasoncode.ReasonCodeResolver;
import me.socure.aigateway.rulecode.RuleEvaluator;
import me.socure.aigateway.rulecode.vendors.global.ComputeGlobals;
import me.socure.aigateway.service.vendor.*;
import me.socure.aigateway.service.vendor.trice.TRICEAdapter;
import me.socure.aigateway.service.vendor.trice.model.TriceResponse;
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@Log4j2
public class AiGatewayService {

    private final Executor executor;
    private final VendorAdapterFactory vendorAdapterFactory;
    private final VendorCallExecuter vendorCallExecuter;

    private final ComputeGlobals computeGlobals;

    private final ReasonCodeResolver reasonCodeResolver;

    private final TRICEAdapter triceAdapter;
    private final ObjectMapper objectMapper;
    private final RuleEvaluator ruleEvaluator;
    private final DynamicControlCenterV2Evaluate dynamicControlCenterV2Evaluate;

    private final ScheduledExecutorService scheduler;

    @Autowired
    public AiGatewayService(final Executor executor, final VendorAdapterFactory vendorAdapterFactory, final VendorCallExecuter vendorCallExecuter, ComputeGlobals computeGlobals, ReasonCodeResolver reasonCodeResolver,
                            TRICEAdapter triceAdapter, ObjectMapper objectMapper, RuleEvaluator ruleEvaluator,
                            DynamicControlCenterV2Evaluate dynamicControlCenterV2Evaluate, ScheduledExecutorService scheduledExecutorService) {
        this.executor = executor;
        this.vendorAdapterFactory = vendorAdapterFactory;
        this.vendorCallExecuter = vendorCallExecuter;
        this.computeGlobals = computeGlobals;
        this.reasonCodeResolver = reasonCodeResolver;
        this.triceAdapter = triceAdapter;
        this.objectMapper = objectMapper;
        this.ruleEvaluator = ruleEvaluator;
        this.dynamicControlCenterV2Evaluate = dynamicControlCenterV2Evaluate;
        this.scheduler = scheduledExecutorService;
    }

    public CompletableFuture<AiGatewayResponseWrapper> processRequestAsync(final AIGatewayRequest request) {
        CompletableFuture<Boolean> flagFuture = FlagEvaluatorBridge.evaluateFlag(
                dynamicControlCenterV2Evaluate,
                Flags.FLAG_GROUP_NAME,
                Flags.ENABLE_MBTVL_EARLY_FLOW,
                String.valueOf(request.metadata().getAccountId())
        );

        return flagFuture.thenCompose(isFlagEnabled ->
                executeVendorFlowWithOptionalEarlyExit(request, isFlagEnabled)
        );
    }

    private CompletableFuture<AiGatewayResponseWrapper> executeVendorFlowWithOptionalEarlyExit(
            AIGatewayRequest request,
            boolean enableEarlyReturn
    ) {
        return CompletableFuture
                .supplyAsync(() -> validateRequest(request), executor)
                .thenComposeAsync(this::getAiVendorAggregatedRequest, executor)
                .thenComposeAsync(aiVendorRequest -> {
                    CompletableFuture<AiGatewayResponseWrapper> resultFuture = new CompletableFuture<>();
                    BiConsumer<List<VendorRawResponse>, Boolean> tryComplete = buildIdempotentResponseCallback(request, aiVendorRequest, resultFuture);

                    CompletableFuture<AIVendorResponse> httpFuture = vendorCallExecuter.callAiVendorService(aiVendorRequest);
                    CompletableFuture<List<VendorRawResponse>> dynFuture = callDynamoForVendors(request);
                    CompletableFuture<VendorRawResponse> triceFuture = initiateTriceWorkflow(request);

                    if (enableEarlyReturn) {
                        scheduler.schedule(() -> {
                            try {
                                AIVendorResponse httpResp = httpFuture.getNow(null);
                                List<VendorRawResponse> dynResp = dynFuture.getNow(null);
                                VendorRawResponse triceResp = triceFuture.getNow(null);
                                if (httpResp == null || httpResp.getResponses() == null || triceResp != null) return;
                                List<VendorRawResponse> batch = new ArrayList<>(httpResp.getResponses());
                                if (dynResp != null) batch.addAll(dynResp);
                                Optional<VendorRawResponse> mbtvlRaw = batch.stream()
                                        .filter(r -> "MBTVL".equalsIgnoreCase(r.getVendorName()))
                                        .findFirst();
                                Optional<String> code = mbtvlRaw.flatMap(r ->
                                        ruleEvaluator.evaluateMBTVL100004(request, r)
                                );
                                if (code.filter(c -> Set.of("A", "W", "D").contains(c)).isPresent()) {
                                    tryComplete.accept(batch, true);
                                }
                            } catch (Throwable t) {
                                // falling back to normal flow
                                log.error("Error during early exit processing", t);
                            }
                        }, 2400, TimeUnit.MILLISECONDS);
                    }

                    CompletableFuture
                            .allOf(httpFuture, dynFuture, triceFuture)
                            .thenRunAsync(() -> {
                                List<VendorRawResponse> all = new ArrayList<>();
                                try {
                                    AIVendorResponse h = httpFuture.get(6000, TimeUnit.MILLISECONDS);
                                    if (h.getResponses() != null) all.addAll(h.getResponses());
                                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                                    log.error("HTTP vendor service call failed", e);
                                    all.addAll(createFailedHttpVendorResponses(aiVendorRequest));
                                }
                                try {
                                    List<VendorRawResponse> d = dynFuture.get(6000, TimeUnit.MILLISECONDS);
                                    if (d != null) all.addAll(d);
                                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                                    log.error("DynamoDB vendor calls failed", e);
                                }
                                try {
                                    VendorRawResponse t = triceFuture.get(6000, TimeUnit.MILLISECONDS);
                                    if (t != null) all.add(t);
                                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                                    log.error("Trice workflow failed", e);
                                    all.add(new VendorRawResponse(500, "TRICE", null));
                                }

                                tryComplete.accept(all, false);
                            }, executor);
                    return resultFuture;
                }, executor);
    }

    private BiConsumer<List<VendorRawResponse>, Boolean> buildIdempotentResponseCallback(AIGatewayRequest request, AIVendorRequest aiVendorRequest, CompletableFuture<AiGatewayResponseWrapper> resultFuture) {
        AtomicBoolean processed = new AtomicBoolean(false);
        BiConsumer<List<VendorRawResponse>, Boolean> tryComplete = (responses, early) -> {
            if (processed.compareAndSet(false, true)) {
                try {
                    AiGatewayResponseWrapper resp =
                            processVendorResponse(responses, aiVendorRequest, request, early);
                    resultFuture.complete(resp);
                } catch (Exception e) {
                    log.error("Error processing vendor response", e);
                    resultFuture.completeExceptionally(e);
                }
            }
        };
        return tryComplete;
    }

    private CompletableFuture<VendorRawResponse> initiateTriceWorkflow(AIGatewayRequest request) {
        if (request.vendors().stream().noneMatch(v -> "trice".equalsIgnoreCase(v.getName()))) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<TriceResponse> triceResponseFuture = triceAdapter.getTriceVendorResponseWithCaching(request);

        return triceResponseFuture.thenApply(response -> {
            JsonNode parsedJson = null;
            if (response != null) {
                try {
                    String json = objectMapper.writeValueAsString(response);
                    parsedJson = objectMapper.readTree(json);
                } catch (Exception e) {
                    log.error("Failed to parse TriceResponse JSON for txnId={}, response={}", request.metadata().getTransactionId(), response, e);
                }
            } else {
                log.warn("TriceResponse was null for txnId={}", request.metadata().getTransactionId());
            }
            return VendorRawResponse.builder().status(200).response(parsedJson).vendorName("trice").build();
        });
    }

    private AIGatewayRequest validateRequest(final AIGatewayRequest request) {
        if (request.vendors().isEmpty()) {
            throw new BadRequestException("vendor size should be equal to more then 1");
        }
        return request;
    }

    private CompletableFuture<AIVendorRequest> getAiVendorAggregatedRequest(final AIGatewayRequest request) {
        List<AIGatewayVendorConfig> vendorConfigs = request.vendors();
        if (StringUtils.hasText(request.params().getNationalId()) && StringUtils.hasText(request.params().getFirstName()) && StringUtils.hasText(request.params().getSurName()))
            vendorConfigs.add(AIGatewayVendorConfig.builder().name("vrval").timeoutInMillis(4000L).build());
        List<CompletableFuture<Map.Entry<String, VendorConfig>>> futures = new ArrayList<>();
        for (AIGatewayVendorConfig vendorConfig : vendorConfigs) {
            futures.add(processVendorConfigAsync(vendorConfig, request));
        }

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(v -> collectVendorConfigs(request, futures));
    }

    private CompletableFuture<Map.Entry<String, VendorConfig>> processVendorConfigAsync(AIGatewayVendorConfig vendorConfig, AIGatewayRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            VendorAdapter vendorAdapter = vendorAdapterFactory.getVendorAdapter(vendorConfig.getName());
            if (vendorAdapter.getCallType(request).isHttpCallAllowed()) {
                VendorConfig config = vendorAdapter.populateVendorConfig(request);
                return Map.entry(vendorConfig.getName(), config);
            }
            return null;
        }, executor);
    }

    private AIVendorRequest collectVendorConfigs(AIGatewayRequest request, List<CompletableFuture<Map.Entry<String, VendorConfig>>> futures) {
        Map<String, VendorConfig> aiVendorConfigs = new HashMap<>();
        List<String> vendorList = new ArrayList<>();

        for (CompletableFuture<Map.Entry<String, VendorConfig>> future : futures) {
            Map.Entry<String, VendorConfig> entry = future.join();
            if (entry != null) {
                aiVendorConfigs.put(entry.getKey().toLowerCase(), entry.getValue());
                vendorList.add(entry.getKey().toLowerCase());
            }
        }

        AIVendorRequest aiVendorRequest = request.toVendorRequest();
        aiVendorRequest.setVendorConfigs(aiVendorConfigs);
        aiVendorRequest.setVendors(vendorList);
        return aiVendorRequest;
    }

    private AiGatewayResponseWrapper processVendorResponse(List<VendorRawResponse> vendorResponse, AIVendorRequest aiVendorRequest,
                                                           AIGatewayRequest aiGatewayRequest, boolean skipTriceFlow) {

        List<me.socure.aigateway.model.vendor.AiResponseData> vendorResponses = new ArrayList<>();
        for (VendorRawResponse response : vendorResponse) {
            try {
                String vendorName = response.getVendorName();

                // Check if this is a failed vendor response (non-2xx status or null response)
                if (!HttpStatusCode.valueOf(response.getStatus()).is2xxSuccessful()) {
                    log.warn("Vendor {} returned failed status {} or null response", vendorName, response.getStatus());
                    AiResponseData failedResponse = createFailedVendorResponse(vendorName, aiVendorRequest.getTransactionId());
                    vendorResponses.add(failedResponse);
                    continue;
                }

                VendorAdapter vendorAdapter = vendorAdapterFactory.getVendorAdapter(vendorName);
                VendorParsedResponse parsed = vendorAdapter.processResponse(response);

                // Check if parsing failed (parsedData is null for failed responses)
                if (response.getResponse() != null && parsed.getParsedData() == null) {
                    log.warn("Vendor {} parsing failed, parsedData is null", vendorName);
                    AiResponseData failedResponse = createFailedVendorResponse(vendorName, aiVendorRequest.getTransactionId());
                    vendorResponses.add(failedResponse);
                    continue;
                }

                Object preProcessRuleCodes = vendorAdapter.preProcessRuleCodes(parsed, aiVendorRequest);
                AIVendorRuleCodes ruleCodeResp = vendorAdapter.computeRuleCode(parsed, aiVendorRequest, preProcessRuleCodes);
                AiResponseData responseData = vendorAdapter.getVendorResponse(parsed, aiVendorRequest, ruleCodeResp);
                if (responseData != null) {
                    vendorResponses.add(responseData);
                } else {
                    log.warn("Vendor {} getVendorResponse returned null", vendorName);
                    AiResponseData failedResponse = createFailedVendorResponse(vendorName, aiVendorRequest.getTransactionId());
                    vendorResponses.add(failedResponse);
                }
            } catch (Exception e) {
                log.error("Failed to process vendor: {}", response.getVendorName(), e);
                AiResponseData failedResponse = createFailedVendorResponse(response.getVendorName(), aiVendorRequest.getTransactionId());
                vendorResponses.add(failedResponse);
            }
        }

        if (skipTriceFlow) {
            vendorResponses.add(triceResponseData(aiGatewayRequest.metadata().getTransactionId()));
        }

        AIVendorRuleCodes globalRuleCode = computeGlobals.computeGlobalRuleCode(aiGatewayRequest, vendorResponses);

        AIGatewayGlobalRulecodes aiGatewayGlobalRulecodes = AIGatewayGlobalRulecodes.builder()
                .numericalRulecodes(globalRuleCode.getNumericalRulecodes())
                .categoricalRulecodes(globalRuleCode.getCategoricalRulecodes())
                .build();

        Set<String> reasonCodesSet;
        try {
            List<String> rcList = reasonCodeResolver.resolve(globalRuleCode, aiGatewayRequest).get(1000, TimeUnit.MINUTES);
            reasonCodesSet = new HashSet<>(rcList);
        } catch (InterruptedException | java.util.concurrent.ExecutionException | TimeoutException ex) {
            log.error("Error fetching reason codes, returning empty set", ex);
            reasonCodesSet = Set.of();
        }

        AIGatewayResponse aiGatewayResponse = AIGatewayResponse.builder()
                .vendorResponses(vendorResponses)
                .globalRulecodes(aiGatewayGlobalRulecodes)
                .reasonCodes(reasonCodesSet).build();

        return AiGatewayResponseWrapper.builder().status("ok").data(aiGatewayResponse).build();
    }

    private CompletableFuture<List<VendorRawResponse>> callDynamoForVendors(AIGatewayRequest req) {
        List<VendorRawResponse> results = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<?>> callbacks = new ArrayList<>();

        for (AIGatewayVendorConfig vc : req.vendors()) {
            VendorAdapter adapter = vendorAdapterFactory.getVendorAdapter(vc.getName());
            if (!adapter.getCallType(req).isDynamoCallAllowed()) continue;

            CompletableFuture<?> cb = adapter.fetchFromDynamoAsync(req)
                    .thenAccept(json -> results.add(new VendorRawResponse(200, vc.getName(), json)))
                    .exceptionally(ex -> {
                        log.error("Dynamo fetch failed for vendor {}: ", vc.getName(), ex);
                        results.add(new VendorRawResponse(500, vc.getName(), null));
                        return null;
                    });

            callbacks.add(cb);
        }

        return CompletableFuture
                .allOf(callbacks.toArray(new CompletableFuture[0]))
                .thenApply(ignored -> results);
    }

    private AiResponseData triceResponseData(String txnId) {
        Map<String, Double> numericalMap = Map.of("TRICE.100136", 1.0);
        return AiResponseData.builder()
                .metadata(AIVendorMetadata.builder()
                        .name("TRICE")
                        .success(true)
                        .statusCode("SKIPPED")
                        .vendorReferenceId("")
                        .transactionId(txnId)
                        .build())
                .result(AIVendorResult.builder().build())
                .rulecodes(AIVendorRuleCodes.builder().numericalRulecodes(numericalMap).build())
                .build();
    }

    private AiResponseData createFailedVendorResponse(String vendorName, String transactionId) {
        return AiResponseData.builder()
                .metadata(AIVendorMetadata.builder()
                        .name(vendorName.toUpperCase())
                        .success(false)
                        .statusCode("FAILED")
                        .vendorReferenceId("")
                        .transactionId(transactionId)
                        .build())
                .result(AIVendorResult.builder().build())
                .rulecodes(getErrorRuleCode(vendorName))
                .error(null)
                .build();
    }

    private AIVendorRuleCodes getErrorRuleCode(String vendorName) {
        Map<String, Double> numericalRulecodes = new HashMap<>();
        numericalRulecodes.put(vendorName.toUpperCase() + ".100130", 1.0);
        return AIVendorRuleCodes.builder()
                .numericalRulecodes(numericalRulecodes)
                .categoricalRulecodes(Collections.emptyMap())
                .build();
    }

    private List<VendorRawResponse> createFailedHttpVendorResponses(AIVendorRequest aiVendorRequest) {
        List<VendorRawResponse> failedResponses = new ArrayList<>();
        for (String vendorName : aiVendorRequest.getVendors()) {
            failedResponses.add(new VendorRawResponse(500, vendorName, null));
        }
        return failedResponses;
    }


}
