package me.socure.aigateway.service.vendor.convl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.NullNode;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.bme.ConvlBme;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.vendor.*;
import me.socure.aigateway.rulecode.RulecodeHelper;
import me.socure.aigateway.service.vendor.CallType;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.store.SaiLookup;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Component
@Log4j2
public class CONVLAdapter implements VendorAdapter {

    private final SaiLookup saiLookup;
    private final Executor executor;
    private final ObjectMapper objectMapper;
    private final ConvlBme convlBme;
    private final String vendor = "CONVL";

    private final RulecodeHelper rulecodeHelper;

    public CONVLAdapter(SaiLookup saiLookup, Executor executor, ObjectMapper objectMapper, ConvlBme convlBme, RulecodeHelper rulecodeHelper) {
        this.saiLookup = saiLookup;
        this.executor = executor;
        this.objectMapper = objectMapper;
        this.convlBme = convlBme;
        this.rulecodeHelper = rulecodeHelper;
    }

    @Override
    public CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        return CallType.builder().isDynamoCallAllowed(true).build();
    }

    public CompletableFuture<JsonNode> fetchFromDynamoAsync(AIGatewayRequest req) {
        return saiLookup
                .getAccountDataV2(req).thenApplyAsync(saiResp -> {
                    try {
                        return (saiResp != null) ? objectMapper.readTree(saiResp.getData()) : null;
                    } catch (Exception e) {
                        log.error("Error parsing Dynamo JSON", e);
                        return objectMapper.createObjectNode();
                    }
                }, executor);
    }

    @Override
    public JsonNode preProcessRuleCodes(VendorParsedResponse parsedResponse,
                                        AIVendorRequest req) {
        JsonNode resp = Optional.ofNullable(parsedResponse)
                .map(VendorParsedResponse::getParsedData)
                .filter(JsonNode.class::isInstance)
                .map(JsonNode.class::cast)
                .orElse(NullNode.instance);
        return convlBme.getBmeEntity(req.getPiis().getAccountNumber(), req.getPiis().getRoutingNumber(), resp);
    }

    @Override
    public <T> AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T preProcessRuleCodes) {
        Map<String, Double> numericalRulecodes = new HashMap<>();
        Map<String, String> categoricalRulecodes = new HashMap<>();

        JsonNode dbResp = vendorResponse.getParsedData() != null ? (JsonNode) vendorResponse.getParsedData() : null;

        JsonNode bmeEntity = Optional.ofNullable(preProcessRuleCodes)
                .filter(JsonNode.class::isInstance)
                .map(JsonNode.class::cast)
                .orElse(null);

        double lookupVal = dbResp != null ? 1 : 0;
        numericalRulecodes.put(vendor.toUpperCase() + ".100000", lookupVal);
        numericalRulecodes.put(vendor.toUpperCase() + ".100135", 1.0);

        if (dbResp == null) {
            return new AIVendorRuleCodes(numericalRulecodes, categoricalRulecodes);
        }
        AIVendorRuleCodes aiVendorRuleCodes = rulecodeHelper.executeVendorFunctionCommon(vendor.toLowerCase(), aiVendorRequest, vendorResponse, bmeEntity, false, null);
        aiVendorRuleCodes.getNumericalRulecodes().putAll(numericalRulecodes);
        return aiVendorRuleCodes;
    }

    @Override
    public AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp) {
        AIVendorMetadata metadata = AIVendorMetadata.builder()
                .name(vendor.toUpperCase())
                .statusCode("COMPLETED")
                .success(true)
                .transactionId(aiVendorRequest.getTransactionId())
                .vendorReferenceId("")
                .build();
        return AiResponseData.builder().metadata(metadata).rulecodes(ruleCodeResp).build();
    }
}