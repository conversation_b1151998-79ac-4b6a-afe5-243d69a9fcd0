package me.socure.aigateway.service;

import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate;
import scala.Option;
import scala.Tuple2;
import scala.compat.java8.FutureConverters;

import java.util.concurrent.CompletableFuture;

public class FlagEvaluatorBridge {

    public static CompletableFuture<Boolean> evaluateFlag(
            DynamicControlCenterV2Evaluate evaluator,
            String groupName,
            String flagName,
            String accountId
    ) {
        return FutureConverters.toJava(
                evaluator.evaluate(
                        groupName,
                        flagName,
                        Option.apply(""),
                        Option.apply(Tuple2.apply("accountId", accountId)),
                        new String[0]
                )
        ).thenApply(flagResult ->
                flagResult.isRight() && flagResult.right().get().isFlagActive()
        ).toCompletableFuture();
    }
}
