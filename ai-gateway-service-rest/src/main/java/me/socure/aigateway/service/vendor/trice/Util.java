package me.socure.aigateway.service.vendor.trice;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.response.AIVendorResponse;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.service.vendor.trice.model.TriceResponse;

import java.util.Optional;

@Log4j2
public class Util {
    static String generateCacheKey(String routingNumber, String accountNumber) {
        return routingNumber + "-" + accountNumber;
    }

    static TriceResponse parsePollingResponse(AIVendorRequest baseVendorRequest,
                                              AIVendorResponse smartTransferResponse,
                                              String partyId,
                                              String ultimateSendingPartyId,
                                              String uri) {
        String txnId = baseVendorRequest.getTransactionId();
        int statusCode = smartTransferResponse.getStatus();
        String pennyDropMode = TriceAPIConstants.TRANSFER_TYPE_FEDNOW.equalsIgnoreCase(uri) ? "fednow" : "rtp";
        Optional<JsonNode> vendorRawResponse = smartTransferResponse.getResponses().stream().filter(
                response -> response.getVendorName().equalsIgnoreCase("trice")
        ).findFirst().map(VendorRawResponse::getResponse);

        if (TriceAPIConstants.TRICE_API_HTTP_SUCCESS_CODES.contains(statusCode)) {
            if (vendorRawResponse.isPresent()) {
                JsonNode json = vendorRawResponse.get();
                String transferStatus = getOptionalText(json, "status");
                boolean isAccountValidated = TriceAPIConstants.TRANSFER_SUCCESS_STATUS.equalsIgnoreCase(transferStatus);

                String transferId = getOptionalText(json, "id");
                JsonNode stateDetails = json.get("state_details");

                String stateMessage = getOptionalText(stateDetails, "message");
                String stateReason = getOptionalText(stateDetails, "reason");
                String stateReasonCode = getOptionalText(stateDetails, "reason_code");

                String lastSeenDate = null;
                JsonNode enrollment = json.get("enrollment");
                if (enrollment != null && enrollment.get("created") != null) {
                    String created = enrollment.get("created").asText();
                    if (created.length() >= 10) {
                        lastSeenDate = created.substring(0, 10);
                    }
                }

                return TriceResponse.builder()
                        .statusCode(200)
                        .lastApiCalled(uri)
                        .response(vendorRawResponse.toString())
                        .partyId(partyId)
                        .ultimateSendingPartyId(ultimateSendingPartyId)
                        .transferId(transferId)
                        .isAccountValidated(isAccountValidated)
                        .pennyDropStatus(transferStatus)
                        .pennyDropMode(pennyDropMode)
                        .stateDetailsMessage(stateMessage)
                        .stateDetailsReason(stateReason)
                        .stateDetailsReasonCode(stateReasonCode)
                        .lastSeenDate(lastSeenDate)
                        .build();
            } else {
                log.info("Trice Logger : {} Transfer Response parsing failed for TxnID: {}", pennyDropMode, txnId);
                return TriceResponse.builder()
                        .lastApiCalled("rtp_transfer")
                        .response(vendorRawResponse.toString())
                        .partyId(partyId)
                        .ultimateSendingPartyId(ultimateSendingPartyId)
                        .pennyDropMode(pennyDropMode)
                        .build();
            }
        } else {
            log.info("Trice Logger: {} Transfer Request failed with http status code, {} for TxnID: {}",
                    pennyDropMode, statusCode, txnId);

            return TriceResponse.builder()
                    .partyId(partyId)
                    .ultimateSendingPartyId(ultimateSendingPartyId)
                    .response(vendorRawResponse.toString())
                    .build();
        }
    }

    static String getOptionalText(JsonNode node, String fieldName) {
        if (node != null && node.has(fieldName)) {
            JsonNode field = node.get(fieldName);
            return field.isTextual() ? field.asText() : null;
        }
        return null;
    }
}
