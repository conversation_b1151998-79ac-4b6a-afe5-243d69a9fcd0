package me.socure.aigateway.service.vendor;

import com.fasterxml.jackson.databind.JsonNode;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;

import java.util.concurrent.CompletableFuture;

public interface VendorAdapter {

    default CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        return CallType.builder().build();
    }

   default VendorConfig populateVendorConfig(AIGatewayRequest aiGatewayRequest){
        return null;
   }

    default CompletableFuture<JsonNode> fetchFromDynamoAsync(AIGatewayRequest req) {
        return CompletableFuture.completedFuture(null);
    }

    default VendorParsedResponse processResponse(VendorRawResponse vendorResponse) {
        return VendorParsedResponse.<JsonNode>builder()
                .status(vendorResponse.getStatus())
                .vendorName(vendorResponse.getVendorName())
                .parsedData(vendorResponse.getResponse())
                .build();
    }

    <T> T preProcessRuleCodes(VendorParsedResponse vendorParsedResponse, AIVendorRequest aiVendorRequest);

    <T> AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T preProcessRuleCodes);

    AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp);
}
