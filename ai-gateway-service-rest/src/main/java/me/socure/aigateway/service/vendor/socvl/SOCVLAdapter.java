package me.socure.aigateway.service.vendor.socvl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.model.vendor.AIVendorMetadata;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;
import me.socure.aigateway.rulecode.RulecodeHelper;
import me.socure.aigateway.service.vendor.CallType;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorConfigFactory;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import me.socure.aigateway.service.vendor.socvl.model.IdResolutionResponse;
import me.socure.aigateway.service.vendor.socvl.model.SocidResponse;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class SOCVLAdapter implements VendorAdapter {

    String vendorName = "socvl";
    private final VendorConfigFactory vendorConfigFactory;
    private final ObjectMapper objectMapper;
    private final RulecodeHelper rulecodeHelper;

    public SOCVLAdapter(final VendorConfigFactory vendorConfigFactory, ObjectMapper objectMapper, RulecodeHelper rulecodeHelper) {
        this.vendorConfigFactory = vendorConfigFactory;
        this.objectMapper = objectMapper;
        this.rulecodeHelper = rulecodeHelper;
    }
    @Override
    public CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        boolean isBusinessInquiry = aiGatewayRequest.params() != null &&
                                   aiGatewayRequest.params().getBusinessName() != null &&
                                   !aiGatewayRequest.params().getBusinessName().trim().isEmpty();

        boolean hasSSNOrDOB = aiGatewayRequest.params() != null &&
                             ((aiGatewayRequest.params().getNationalId() != null && !aiGatewayRequest.params().getNationalId().trim().isEmpty()) ||
                              (aiGatewayRequest.params().getDob() != null && !aiGatewayRequest.params().getDob().trim().isEmpty()));

        if (isBusinessInquiry || !hasSSNOrDOB) {
            log.info("SOCVL vendor call is being skipped for business inquiries or personal inquiries without SSN or DOB. " +
                    "isBusinessInquiry: {}, hasSSNOrDOB: {}", isBusinessInquiry, hasSSNOrDOB);
            return CallType.builder().build();
        }

        return CallType.builder().isHttpCallAllowed(true).build();
    }

    @Override
    public VendorConfig populateVendorConfig(AIGatewayRequest aiGatewayRequest) {
        return vendorConfigFactory.getVendorConfig(vendorName);
    }

    @Override
    public VendorParsedResponse processResponse(VendorRawResponse vendorResponse) {
        if (!HttpStatusCode.valueOf(vendorResponse.getStatus()).is2xxSuccessful()) {
            return VendorParsedResponse.<IdResolutionResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(null)
                    .build();
        }
        try {
            IdResolutionResponse resp = objectMapper.readValue(vendorResponse.getResponse().toString(), IdResolutionResponse.class);
            return VendorParsedResponse.<IdResolutionResponse>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(resp)
                    .build();
        } catch (Exception e) {
            log.error("Error parsing SOCVL response: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public PreComputedData preProcessRuleCodes(VendorParsedResponse vendorParsedResponse, AIVendorRequest aiVendorRequest) {
        IdResolutionResponse vendorRawResponse = (IdResolutionResponse) vendorParsedResponse.getParsedData();
        SocidResponse bestMatchEntity = SocvlRuleUtil.getBestMatchIdentity(vendorRawResponse);
        String ssnMatch = SocvlRuleUtil.getSSNMatch(bestMatchEntity, aiVendorRequest.getPiis().getNationalId());
        String dobMatch = SocvlRuleUtil.getDOBMatch(bestMatchEntity, aiVendorRequest.getPiis().getDob());
        String deceasedIndicator = SocvlRuleUtil.getDeceasedIndicator(bestMatchEntity);
        return PreComputedData.builder()
                .bestMatchEntity(bestMatchEntity)
                .dobMatch(dobMatch)
                .ssnMatch(ssnMatch)
                .decesedIndicator(deceasedIndicator)
                .build();
    }

    @Override
    public <T> AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T preProcessRuleCodes) {
        if (vendorResponse == null || vendorResponse.getParsedData() == null)
            return AIVendorRuleCodes.builder().build();
        return rulecodeHelper.executeVendorFunctionCommon(vendorName, aiVendorRequest, vendorResponse, preProcessRuleCodes, false, null);
    }

    @Override
    public AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp) {
        AIVendorMetadata metadata = AIVendorMetadata.builder()
                .name("SOCUREID")
                .success(true)
                .statusCode("COMPLETED")
                .vendorReferenceId(aiVendorRequest.getTransactionId())
                .transactionId(aiVendorRequest.getTransactionId())
                .build();
        return AiResponseData.builder()
                .metadata(metadata)
                .rulecodes(ruleCodeResp)
                .build();
    }
}