package me.socure.aigateway.service.vendor.vrval;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import me.socure.aigateway.model.request.AIGatewayRequest;
import me.socure.aigateway.model.request.AIVendor.AIVendorRequest;
import me.socure.aigateway.model.request.AIVendor.VendorConfig;
import me.socure.aigateway.model.response.VendorRawResponse;
import me.socure.aigateway.model.vendor.AIVendorRuleCodes;
import me.socure.aigateway.model.vendor.AiResponseData;
import me.socure.aigateway.service.vendor.CallType;
import me.socure.aigateway.service.vendor.VendorAdapter;
import me.socure.aigateway.service.vendor.VendorConfigFactory;
import me.socure.aigateway.service.vendor.VendorParsedResponse;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Log4j2
@Component
public class VRVALAdapter implements VendorAdapter {

    private final VendorConfigFactory vendorConfigFactory;
    String vendorName = "vrval";
    public VRVALAdapter(final VendorConfigFactory vendorConfigFactory, ObjectMapper objectMapper) {
        this.vendorConfigFactory = vendorConfigFactory;
    }

    public CallType getCallType(AIGatewayRequest aiGatewayRequest) {
        return CallType.builder().isHttpCallAllowed(true).build();
    }

    @Override
    public VendorConfig populateVendorConfig(AIGatewayRequest aiGatewayRequest) {
        String submissionDate = convertLongToDate(aiGatewayRequest.metadata().getSubmissionDate());
        VendorConfig vendorConfig = vendorConfigFactory.getVendorConfig(vendorName.toLowerCase());
        vendorConfig.setSubmissionDate(submissionDate);
        return vendorConfig;
    }

    @Override
    public VendorParsedResponse processResponse(VendorRawResponse vendorResponse) {
        if (!HttpStatusCode.valueOf(vendorResponse.getStatus()).is2xxSuccessful()) {
            return VendorParsedResponse.<JsonNode>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(null)
                    .build();
        }
        try {
            return VendorParsedResponse.<JsonNode>builder()
                    .status(vendorResponse.getStatus())
                    .vendorName(vendorResponse.getVendorName())
                    .parsedData(vendorResponse.getResponse())
                    .build();
        } catch (Exception e) {
            log.error("Error parsing VRVAL response: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public <T> T preProcessRuleCodes(VendorParsedResponse vendorParsedResponse, AIVendorRequest aiVendorRequest) {
        return null;
    }

    @Override
    public <T> AIVendorRuleCodes computeRuleCode(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, T preProcessRuleCodes) {
        JsonNode node = (JsonNode)vendorResponse.getParsedData();
        if (node == null) {
            log.error("No valid vendor response found for vendor 'vrval'.");
            return null;
        }

        JsonNode entityNode = node.path("data").path("entity");
        if (entityNode.isMissingNode() || entityNode.isNull()) {
            log.error("Entity node is missing or null in the response: {}", node);
            return null;
        }

        Map<String, Double> numericalRulecodes = new HashMap<>();
        JsonNode numericalNode = entityNode.path("numerical");
        if (!numericalNode.isMissingNode() && !numericalNode.isNull()) {
            numericalNode.fields().forEachRemaining(entry ->
                    numericalRulecodes.put("VRVAL." + entry.getKey(), entry.getValue().asDouble())
            );
        } else {
            log.error("Numerical node is missing or null in the entity: {}", entityNode);
        }

        Map<String, String> categoricalRulecodes = new HashMap<>();
        JsonNode categoricalNode = entityNode.path("categorical");
        if (!categoricalNode.isMissingNode() && !categoricalNode.isNull()) {
            categoricalNode.fields().forEachRemaining(entry ->
                    categoricalRulecodes.put("VRVAL." + entry.getKey(), entry.getValue().asText())
            );
        } else {
            log.error("Categorical node is missing or null in the entity: {}", entityNode);
        }

        return AIVendorRuleCodes.builder()
                .numericalRulecodes(numericalRulecodes)
                .categoricalRulecodes(categoricalRulecodes)
                .build();
    }

    @Override
    public AiResponseData getVendorResponse(VendorParsedResponse vendorResponse, AIVendorRequest aiVendorRequest, AIVendorRuleCodes ruleCodeResp) {
        return null;
    }

    public static String convertLongToDate(long epochMilli) {
        Date date = new Date(epochMilli);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

}