server.port: 5000

dynamoDB:
  table:
    name: rc_config_unified
    config-key: rc_table_config_ds
    lookup-key: sai_lookup
    name-refresh-interval: 60000 #in milliseconds

executioncontext:
  maxPoolSize: 100
  corePoolSize: 100
  queueSize: 1000
  threadNamePrefix: ai-gateway-service-

scheduler:
  poolSize: 100
  threadNamePrefix: ai-gateway-service-scheduler-

caffeine:
  maximumSize: 5000
  expireAfterWrite: 360000 #in milliseconds
  refreshInterval: 300000 #in milliseconds

vendors:
  configs:
    bnyvl:
      uri: "/payments/v1/inquiry/avs/account-validations"
      httpMethod: "post"
      template: "bnyvl.body.validate"
      headerTemplate: "bnyvl.header.validate"
    mbtvl:
      uri: "/ACHCheckPrescreen/GetReport"
      httpMethod: "post"
      template: "mbtvl.body.validate"
      headerTemplate: "mbtvl.header.validate"
    socvl:
      uri: "/search/kyc-entity"
      httpMethod: "post"
      template: "socvl.body.validate"
      headerTemplate: "socvl.header.validate"
    trice:
      uri: "/hub/v1/smart_verify"
      httpMethod: "post"
      template: "trice.body.create-smart-verify-transfer"
      headerTemplate: "trice.header.smart-verify"
    vrval:
      uri: "/search/kyc-entity"
      httpMethod: "post"
      template: "vrval.body.validate"
      headerTemplate: "vrval.header.validate"

ai.vendor.service:
  endpoint: "http://ai-vendor-service"
  path: "/vendor/data"
  connectTimeout: 17000
  readTimeout: 17000
  responseTimeout: 17000

transaction-auditing:
  threadPoolSize: 30
  aws:
    maxRetries: 10
    primary:
      sqsRegion: "us-east-1"
      sqsThirdPartyQueueName: "third-party-transaction-auditing-ds"
      sqsWaitTimeSeconds: 20
      sqsMaxBatchSize: 10
      sqsMaxBufferSize: 60
      sqsThirdPartyMaxInFlight: 15
    fallback0:
      sqsRegion: "us-west-2"
      sqsThirdPartyQueueName: "third-party-transaction-auditing-ds"
      sqsWaitTimeSeconds: 20
      sqsMaxBatchSize: 10
      sqsMaxBufferSize: 60
      sqsThirdPartyMaxInFlight: 15
    fallback1:
      sqsRegion: "us-east-2"
      sqsThirdPartyQueueName: "third-party-transaction-auditing-ds"
      sqsWaitTimeSeconds: 20
      sqsMaxBatchSize: 10
      sqsMaxBufferSize: 60
      sqsThirdPartyMaxInFlight: 15
    s3LargeFilesBucket: "sqs-storage-ds"
    s3ThirdPartyRegion: "us-east-1"
    s3ThirdPartyBucket: "thirdparty-stats-ds"
    sqsMinBackOff: 2
    sqsMaxBackOff: 32

reasoncode:
  engine:
    grpc:
      host: "reasoncode-engine"
      port: 81
      use:
        tls: false
    timeout:
      millis: 500

cache:
  dynamo:
    trice:
      table: "sai_trice_transfer_cache_ds"
      partitionKey: "id"
      ttlKey: "expiresOn"
      ttl: 72 # in hours

dynamic.control.center:
  s3.bucketName: "globalconfig-ds"
  memcached:
    host: "vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
    port: 11211
    ttl: 86400
  local.cache.timeout.minutes: 2