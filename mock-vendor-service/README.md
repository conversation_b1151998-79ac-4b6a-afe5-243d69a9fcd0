# AI Vendor Mock Service

A Node.js mock service that simulates AI vendor responses for the ai-gateway-v2 system.

## Overview

This service provides mock responses that match the `AIVendorResponse` type structure used in the ai-gateway-v2 codebase. It runs on `http://localhost:5000` and serves the `/vendor/data` endpoint as specified in the configuration.

## Quick Start

1. **Install dependencies:**
   ```bash
   cd mock-vendor-service
   npm install
   ```

2. **Start the service:**
   ```bash
   npm start
   ```
   
   Or for development with auto-reload:
   ```bash
   npm run dev
   ```

3. **Service will be available at:**
   - Main endpoint: `http://localhost:5000/vendor/data`
   - Health check: `http://localhost:5000/health`
   - Service info: `http://localhost:5000/`

## API Endpoints

### POST/GET `/vendor/data`
Returns a mock `AIVendorResponse` with the following structure:

```json
{
  "status": "SUCCESS",
  "data": {
    "metadata": {
      "name": "MOCK_VENDOR",
      "success": true,
      "statusCode": "COMPLETED",
      "vendorReferenceId": "ref_1234567890",
      "transactionId": "txn_1234567890_abcdef123"
    },
    "result": {
      "ownership": {
        "code": "VERIFIED",
        "description": "Account ownership verified successfully"
      },
      "status": {
        "code": "ACTIVE",
        "description": "Account is active and in good standing",
        "mbDecisionCode": "APPROVE",
        "mbLastSeen": "2024-01-15",
        "ewsDirectContributor": "Y"
      }
    },
    "error": null,
    "rulecodes": {
      "numericalRulecodes": {
        "MOCK_VENDOR.100000": 1.0,
        "MOCK_VENDOR.100001": 0.95,
        "MOCK_VENDOR.100002": 0.87,
        "MOCK_VENDOR.200001": 850.0,
        "MOCK_VENDOR.300001": 0.92
      },
      "categoricalRulecodes": {
        "MOCK_VENDOR.RISK_LEVEL": "LOW",
        "MOCK_VENDOR.VERIFICATION_STATUS": "VERIFIED",
        "MOCK_VENDOR.ACCOUNT_TYPE": "CHECKING",
        "MOCK_VENDOR.OWNERSHIP_CONFIDENCE": "HIGH"
      }
    }
  }
}
```

### Error Simulation
To simulate error responses, you can:
- Add `?error=true` query parameter
- Include `"simulateError": true` in POST request body

### GET `/health`
Returns service health status.

### GET `/`
Returns service information and available endpoints.

## Configuration

The service matches the configuration specified:
```yaml
ai.vendor.service:
  endpoint: "http://localhost:5000"
  path: "/vendor/data"
```

## Response Structure

The mock service returns responses that conform to the Java `AIVendorResponse` class structure:

- **status**: String indicating overall response status
- **data**: `AiResponseData` object containing:
  - **metadata**: Vendor metadata (name, success, statusCode, etc.)
  - **result**: Account ownership and status information
  - **error**: Error information (null for successful responses)
  - **rulecodes**: Numerical and categorical rule codes

## Testing

You can test the service using curl:

```bash
# GET request
curl http://localhost:5000/vendor/data

# POST request
curl -X POST http://localhost:5000/vendor/data \
  -H "Content-Type: application/json" \
  -d '{"transactionId": "test-123"}'

# Error simulation
curl http://localhost:5000/vendor/data?error=true

# Health check
curl http://localhost:5000/health
```

## Development

- The service logs all incoming requests for debugging
- Use `npm run dev` for development with auto-reload
- Modify `server.js` to customize response data or add new endpoints
