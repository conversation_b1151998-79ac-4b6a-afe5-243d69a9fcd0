const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    console.log('Headers:', req.headers);
    if (req.body && Object.keys(req.body).length > 0) {
        console.log('Body:', JSON.stringify(req.body, null, 2));
    }
    next();
});

// Vendor-specific rulecodes based on the provided mapping
function getVendorRulecodes(vendorName) {
    const vendor = vendorName.toUpperCase();

    switch (vendor) {
        case "TRICE":
            return {
                numericalRulecodes: {},
                categoricalRulecodes: {
                    "TRICE.100003": "Bank account blocked", // decides GLOBAL.300906 and GLOBAL.300907
                    "TRICE.100005": "failed"               // triggers GLOBAL.300905
                }
            };

        case "BNYVL":
            return {
                numericalRulecodes: {},
                categoricalRulecodes: {
                    "BNYVL.100001": "VALID",
                    "BNYVL.100002": "Account Terminated",   // triggers GLOBAL.300906
                    "BNYVL.100020": "N"
                }
            };

        case "CONVL":
            return {
                numericalRulecodes: {},
                categoricalRulecodes: {
                    "CONVL.100002": "Closed"               // triggers GLOBAL.300906
                }
            };

        case "MICROBILT":
        case "MBTVL":
            return {
                numericalRulecodes: {},
                categoricalRulecodes: {
                    "MBTVL.100004": "A",
                    "MBTVL.100005": "2023-01-01",
                    "MBTVL.100008": "Account number is invalid", // triggers GLOBAL.300908
                    "MBTVL.100010": "10",
                    "MBTVL.100011": "15"
                }
            };

        default:
            // Default mock vendor rulecodes
            return {
                numericalRulecodes: {
                    "MOCK_VENDOR.100000": 1.0,
                    "MOCK_VENDOR.100001": 0.95,
                    "MOCK_VENDOR.100002": 0.87,
                    "MOCK_VENDOR.200001": 850.0,
                    "MOCK_VENDOR.300001": 0.92
                },
                categoricalRulecodes: {
                    "MOCK_VENDOR.RISK_LEVEL": "LOW",
                    "MOCK_VENDOR.VERIFICATION_STATUS": "VERIFIED",
                    "MOCK_VENDOR.ACCOUNT_TYPE": "CHECKING",
                    "MOCK_VENDOR.OWNERSHIP_CONFIDENCE": "HIGH"
                }
            };
    }
}

// Mock AIVendorResponse generator
function generateMockAIVendorResponse(transactionId = null, vendorName = "MOCK_VENDOR") {
    const mockTransactionId = transactionId || `txn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const rulecodes = getVendorRulecodes(vendorName);

    return {
        status: "SUCCESS",
        data: {
            metadata: {
                name: vendorName.toUpperCase(),
                success: true,
                statusCode: "COMPLETED",
                vendorReferenceId: `ref_${Date.now()}`,
                transactionId: mockTransactionId
            },
            result: {
                ownership: {
                    code: "VERIFIED",
                    description: "Account ownership verified successfully"
                },
                status: {
                    code: "ACTIVE",
                    description: "Account is active and in good standing",
                    mbDecisionCode: "APPROVE",
                    mbLastSeen: "2024-01-15",
                    ewsDirectContributor: "Y"
                }
            },
            error: null,
            rulecodes: rulecodes
        }
    };
}

// Alternative response for error scenarios
function generateErrorAIVendorResponse(transactionId = null, vendorName = "MOCK_VENDOR", errorCode = "VENDOR_ERROR", errorDescription = "Mock vendor error") {
    const mockTransactionId = transactionId || `txn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    return {
        status: "ERROR",
        data: {
            metadata: {
                name: vendorName.toUpperCase(),
                success: false,
                statusCode: "FAILED",
                vendorReferenceId: `ref_${Date.now()}`,
                transactionId: mockTransactionId
            },
            result: {
                ownership: null,
                status: null
            },
            error: {
                code: errorCode,
                description: errorDescription
            },
            rulecodes: null
        }
    };
}

// Main vendor endpoint
app.post('/vendor/data', (req, res) => {
    console.log('=== Vendor Data Request ===');

    // Extract transaction ID and vendor name from request
    const transactionId = req.body?.transactionId || req.headers['x-transaction-id'];
    const vendorName = req.body?.vendor || req.query.vendor || req.headers['x-vendor-name'] || "MOCK_VENDOR";

    // Simulate different responses based on request data
    const shouldSimulateError = req.body?.simulateError === true || req.query.error === 'true';

    console.log(`Processing request for vendor: ${vendorName}`);

    if (shouldSimulateError) {
        console.log('Simulating error response');
        const errorResponse = generateErrorAIVendorResponse(transactionId, vendorName, "MOCK_ERROR", "Simulated vendor error for testing");
        res.status(200).json(errorResponse);
    } else {
        console.log('Returning success response');
        const successResponse = generateMockAIVendorResponse(transactionId, vendorName);
        res.status(200).json(successResponse);
    }
});

// GET endpoint for simple testing
app.get('/vendor/data', (req, res) => {
    console.log('=== Vendor Data GET Request ===');

    const transactionId = req.query.transactionId || req.headers['x-transaction-id'];
    const vendorName = req.query.vendor || req.headers['x-vendor-name'] || "MOCK_VENDOR";
    const shouldSimulateError = req.query.error === 'true';

    console.log(`Processing GET request for vendor: ${vendorName}`);

    if (shouldSimulateError) {
        console.log('Simulating error response via GET');
        const errorResponse = generateErrorAIVendorResponse(transactionId, vendorName, "MOCK_ERROR", "Simulated vendor error for testing");
        res.status(200).json(errorResponse);
    } else {
        console.log('Returning success response via GET');
        const successResponse = generateMockAIVendorResponse(transactionId, vendorName);
        res.status(200).json(successResponse);
    }
});

// Health check endpoint
app.get('/healthcheck', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'ai-vendor-mock-service'
    });
});

// Root endpoint with service info
app.get('/', (req, res) => {
    res.status(200).json({
        service: 'AI Vendor Mock Service',
        version: '1.0.0',
        endpoints: {
            'POST /vendor/data': 'Main vendor data endpoint',
            'GET /vendor/data': 'Vendor data endpoint (GET)',
            'GET /health': 'Health check',
            'GET /': 'Service information'
        },
        usage: {
            'Normal response': 'POST/GET /vendor/data',
            'Vendor-specific response': 'Add "vendor" parameter or X-Vendor-Name header (TRICE, BNYVL, CONVL, MBTVL, MICROBILT)',
            'Error simulation': 'POST/GET /vendor/data?error=true or include "simulateError": true in POST body'
        },
        supportedVendors: ['TRICE', 'BNYVL', 'CONVL', 'MBTVL', 'MICROBILT'],
        examples: {
            'TRICE response': 'POST /vendor/data with {"vendor": "TRICE"}',
            'BNYVL response': 'GET /vendor/data?vendor=BNYVL',
            'Header-based': 'Add X-Vendor-Name: CONVL header'
        }
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Endpoint ${req.method} ${req.path} not found`
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 AI Vendor Mock Service running on http://localhost:${PORT}`);
    console.log(`📋 Main endpoint: http://localhost:${PORT}/vendor/data`);
    console.log(`❤️  Health check: http://localhost:${PORT}/healthcheck`);
    console.log(`📖 Service info: http://localhost:${PORT}/`);
});
