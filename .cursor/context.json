{"name": "AI Gateway v2", "description": "A Spring Boot microservice that acts as a gateway for multiple AI vendors providing account validation and KYC services. The service aggregates responses from various vendors (CONVL, BNYVL, MBTVL, SOCVL, TRICE, VRVAL) and processes them through rule engines to provide unified account validation responses.", "architecture": {"type": "Microservice", "framework": "Spring Boot", "language": "Java 17", "buildTool": "<PERSON><PERSON>", "packaging": "Multi-module Maven project"}, "modules": [{"name": "ai-gateway-service-rest", "description": "Main REST service module containing controllers, services, and business logic", "path": "ai-gateway-service-rest/"}], "keyComponents": {"controllers": [{"name": "AiGatewayController", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/controller/AiGatewayController.java", "description": "Main REST controller for account validation requests", "endpoint": "/account-validation"}, {"name": "ConvlController", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/controller/ConvlController.java", "description": "Controller for CONVL vendor lookup", "endpoint": "/convl-lookup"}, {"name": "VrvalController", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/controller/VrvalController.java", "description": "Controller for VRVAL vendor lookup", "endpoint": "/vrval-lookup"}], "services": [{"name": "AiGatewayService", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/service/AiGatewayService.java", "description": "Core service that orchestrates async calls to multiple AI vendors and aggregates responses"}, {"name": "ConvlService", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/convl/ConvlService.java", "description": "Service for processing CONVL vendor requests"}, {"name": "VrvalService", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/vrval/VrvalService.java", "description": "Service for processing VRVAL vendor requests"}], "vendorAdapters": [{"name": "CONVLAdapter", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/service/vendor/convl/CONVLAdapter.java", "description": "Adapter for CONVL vendor integration"}], "models": [{"name": "AIGatewayRequest", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/model/request/AIGatewayRequest.java", "description": "Main request model for AI Gateway"}, {"name": "AIGatewayResponse", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/model/response/AIGatewayResponse.java", "description": "Main response model for AI Gateway"}], "configuration": [{"name": "AppConfig", "path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/config/AppConfig.java", "description": "Main application configuration"}]}, "vendors": [{"name": "CONVL", "description": "Account validation vendor"}, {"name": "BNYVL", "description": "Bank of New York account validation vendor"}, {"name": "MBTVL", "description": "MicroBilt account validation vendor"}, {"name": "SOCVL", "description": "Socure account validation vendor"}, {"name": "TRICE", "description": "Trice account validation vendor"}, {"name": "VRVAL", "description": "VR account validation vendor"}], "technologies": ["Java 17", "Spring Boot", "Spring Web", "<PERSON><PERSON>", "Lombok", "<PERSON>", "CompletableFuture (Async processing)", "DynamoDB", "AWS S3", "SQS", "gRPC", "<PERSON><PERSON><PERSON><PERSON>"], "keyFeatures": ["Asynchronous processing of multiple vendor calls", "Vendor response aggregation and normalization", "Rule code processing and computation", "Reason code resolution", "Caching with Caffeine", "DynamoDB integration for configuration and lookups", "Third-party audit logging", "Multi-environment configuration (dev, prod)"], "configuration": {"serverPort": {"default": 6000, "dev": 5000, "prod": 5000}, "profiles": ["dev", "prod"], "configFiles": ["ai-gateway-service-rest/src/main/resources/application.yml", "ai-gateway-service-rest/src/main/resources/application-dev.yml", "ai-gateway-service-rest/src/main/resources/application-prod.yml"]}, "buildAndRun": {"build": "mvn clean install", "run": "mvn spring-boot:run", "runWithProfile": "mvn spring-boot:run -Dspring-boot.run.profiles=dev", "test": "mvn test"}, "apiEndpoints": [{"method": "POST", "path": "/account-validation", "description": "Main endpoint for account validation requests"}, {"method": "POST", "path": "/convl-lookup", "description": "Direct CONVL vendor lookup"}, {"method": "POST", "path": "/vrval-lookup", "description": "Direct VRVAL vendor lookup"}], "packageStructure": {"basePackage": "me.socure.aigateway", "subPackages": ["controller - REST controllers", "service - Business logic services", "model.request - Request DTOs", "model.response - Response DTOs", "model.vendor - Vendor-specific models", "config - Configuration classes", "rulecode - Rule processing logic", "bme - Business model entities", "store - Data access layer"]}, "layeredArchitecture": {"description": "The AI Gateway follows a layered rule processing architecture with multiple levels of global rule computation", "layers": {"Level0": {"path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level0.java", "description": "Core scoring and aggregation logic. Processes vendor responses and computes primary scores and rule codes", "responsibilities": ["Aggregates vendor responses", "Computes availability and ownership scores", "Handles BNYVL vs MicroBilt-only scenarios", "Primary score computation and pattern analysis"]}, "Level1": {"path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level1.java", "description": "First level of cross-vendor rule processing. Combines basic vendor rule codes", "responsibilities": ["Cross-vendor validation (e.g., routing number validation across BNYVL and MBTVL)", "Basic vendor response correlation", "Simple boolean logic on vendor outputs"]}, "Level2": {"path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level2.java", "description": "Second level of global rule processing. More complex aggregations and threshold checks", "responsibilities": ["Threshold-based rule processing", "Date-based calculations and comparisons", "Complex aggregation logic"]}, "Level3": {"path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level3.java", "description": "Highest level of global rule processing. Complex business logic and derived rules", "responsibilities": ["Complex business rule derivations", "Multi-vendor pattern matching", "Advanced conditional logic", "Account status determinations (closed, blocked, etc.)"]}, "ComputeGlobals": {"path": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/ComputeGlobals.java", "description": "Orchestrator that executes all global rule levels in sequence", "responsibilities": ["Coordinates execution of Level0, Level1, Level2, Level3", "Manages rule code aggregation across levels", "Filters final output to only include GLOBAL.* rule codes"]}}}, "changeGuidelines": {"vendorSpecificChanges": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/service/vendor/{vendorname}/", "description": "Changes specific to individual vendors (CONVL, BNYVL, MBTVL, SOCVL, TRICE, VRVAL)", "examples": ["New vendor integration", "Vendor-specific response parsing", "Vendor configuration updates", "Vendor adapter modifications"], "keyFiles": ["{VendorName}Adapter.java - Main vendor adapter implementation", "{VendorName}Service.java - Vendor-specific service logic", "model/{vendorname}/ - Vendor-specific models"]}, "globalRuleChanges": {"Level1": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level1.java", "whenToUse": "Simple cross-vendor validations, basic boolean logic on vendor outputs", "examples": ["Routing number validation across multiple vendors", "Simple vendor response correlations", "Basic data consistency checks"]}, "Level2": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level2.java", "whenToUse": "Threshold-based processing, date calculations, moderate complexity aggregations", "examples": ["Age-based thresholds", "Date range validations", "Numeric threshold checks"]}, "Level3": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level3.java", "whenToUse": "Complex business logic, multi-vendor pattern matching, advanced conditional rules", "examples": ["Account status determinations (closed, blocked)", "Complex pattern matching across vendors", "Advanced business rule derivations"]}, "Level0": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/rulecode/vendors/global/Level0.java", "whenToUse": "Core scoring changes, primary aggregation logic modifications", "examples": ["Score computation algorithm changes", "Primary vendor response aggregation", "Core business logic modifications"]}}, "serviceLayerChanges": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/service/", "description": "Business logic, orchestration, and service coordination", "examples": ["Request processing flow changes", "Async processing modifications", "Service orchestration updates"]}, "controllerChanges": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/controller/", "description": "REST API endpoints, request/response handling", "examples": ["New API endpoints", "Request validation changes", "Response format modifications"]}, "configurationChanges": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/config/", "description": "Application configuration, beans, and infrastructure setup", "examples": ["New vendor configurations", "Database connection changes", "Cache configuration updates"]}, "modelChanges": {"location": "ai-gateway-service-rest/src/main/java/me/socure/aigateway/model/", "description": "Data models, DTOs, and request/response structures", "examples": ["New request/response fields", "Model structure changes", "Validation rule updates"]}}, "ruleCodeNamingConvention": {"pattern": "{VENDOR}.{6-digit-number} or GLOBAL.{6-digit-number}", "examples": ["BNYVL.100002 - BNYVL vendor rule code", "MBTVL.100009 - MBTVL vendor rule code", "GLOBAL.300870 - Global rule code computed in Level1", "GLOBAL.300820 - Global rule code computed in Level3"], "globalRuleCodeRanges": {"Level1": "GLOBAL.300870-300899", "Level2": "GLOBAL.300878-300899", "Level3": "GLOBAL.300820-300908"}}, "developmentNotes": ["Uses CompletableFuture for async processing of vendor calls", "Implements vendor adapter pattern for different AI vendors", "Rule codes are processed through a separate engine via gRPC", "Responses are cached using Caffeine for performance", "Configuration is externalized in YAML files per environment", "Uses Lombok for reducing boilerplate code", "Follows Spring Boot best practices for REST API development", "Global rule processing follows a strict layered approach: Level0 → Level1 → Level2 → Level3", "Each global rule method follows naming convention: global{6-digit-number}", "Rule codes are dynamically discovered and executed using reflection", "Vendor adapters implement a common interface for consistent processing"]}